{"cells": [{"cell_type": "markdown", "id": "728cc2b1", "metadata": {}, "source": ["# ScalperGPTWidget Testing and Validation\n", "\n", "This notebook is designed to test and validate the ScalperGPTWidget implementation in Epinnox v6. It includes structured tests for:\n", "\n", "1. Unit testing widget initialization and properties.\n", "2. <PERSON><PERSON>per<PERSON> for widget testing.\n", "3. Simulating UI interactions (sliders, buttons, checkboxes).\n", "4. Testing table data population.\n", "5. Testing signal emissions."]}, {"cell_type": "code", "execution_count": null, "id": "19ce4e77", "metadata": {}, "outputs": [], "source": ["# Import Required Libraries\n", "import unittest\n", "from unittest.mock import MagicMock\n", "from PySide6.QtWidgets import QApplication\n", "from gui.scalper_gpt_widget import ScalperGPTWidget\n", "\n", "# Initialize QApplication for testing\n", "app = QApplication([])"]}, {"cell_type": "code", "execution_count": null, "id": "6df95eee", "metadata": {}, "outputs": [], "source": ["# Unit Testing ScalperGPTWidget UI Components\n", "class TestScalperGPTWidgetUI(unittest.TestCase):\n", "    def setUp(self):\n", "        self.widget = ScalperGPTWidget()\n", "\n", "    def test_ui_components_initialization(self):\n", "        # Verify labels\n", "        self.assertEqual(self.widget.last_analysis_label.text(), \"Not started\")\n", "        self.assertEqual(self.widget.analysis_status_label.text(), \"Initializing...\")\n", "\n", "        # Verify sliders\n", "        self.assertEqual(self.widget.spread_quality_slider.value(), 70)\n", "        self.assertEqual(self.widget.decision_quality_slider.value(), 80)\n", "\n", "        # Verify table\n", "        self.assertEqual(self.widget.opportunities_table.rowCount(), 0)\n", "        self.assertEqual(self.widget.opportunities_table.columnCount(), 6)\n", "\n", "        # Verify buttons\n", "        self.assertTrue(self.widget.manual_analysis_btn.isEnabled())\n", "        self.assertTrue(self.widget.clear_history_btn.isEnabled())\n", "\n", "if __name__ == \"__main__\":\n", "    unittest.main(argv=[''], exit=False)"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}