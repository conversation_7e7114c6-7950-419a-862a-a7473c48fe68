# Import Required Libraries
import unittest
from unittest.mock import MagicMock
from PySide6.QtWidgets import QApplication
from gui.scalper_gpt_widget import ScalperGPTWidget

# Initialize QApplication for testing
app = QApplication([])

# Unit Testing ScalperGPTWidget UI Components
class TestScalperGPTWidgetUI(unittest.TestCase):
    def setUp(self):
        self.widget = ScalperGPTWidget()

    def test_ui_components_initialization(self):
        # Verify labels
        self.assertEqual(self.widget.last_analysis_label.text(), "Not started")
        self.assertEqual(self.widget.analysis_status_label.text(), "Initializing...")

        # Verify sliders
        self.assertEqual(self.widget.spread_quality_slider.value(), 70)
        self.assertEqual(self.widget.decision_quality_slider.value(), 80)

        # Verify table
        self.assertEqual(self.widget.opportunities_table.rowCount(), 0)
        self.assertEqual(self.widget.opportunities_table.columnCount(), 6)

        # Verify buttons
        self.assertTrue(self.widget.manual_analysis_btn.isEnabled())
        self.assertTrue(self.widget.clear_history_btn.isEnabled())

if __name__ == "__main__":
    unittest.main(argv=[''], exit=False)