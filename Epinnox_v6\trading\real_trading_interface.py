"""
Real Trading Interface for Epinnox v6
Integrates CCXT trading engine with position tracking and risk management
"""

from typing import Dict, List, Optional, Tuple, Any
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
import time
import logging

from .ccxt_trading_engine import CCXTTradingEngine
from .position_tracker import PositionTracker

logger = logging.getLogger(__name__)


class RealTradingInterface(QObject):
    """
    Main trading interface that coordinates all trading components
    Provides a clean API for the GUI to interact with
    """
    
    # Signals for UI updates
    order_status_updated = pyqtSignal(dict)  # order info
    position_status_updated = pyqtSignal(dict)  # position info
    balance_status_updated = pyqtSignal(dict)  # balance info
    trading_error = pyqtSignal(str)  # error message
    trading_status = pyqtSignal(str)  # status message
    pnl_updated = pyqtSignal(dict)  # PnL summary
    risk_warning = pyqtSignal(str, str)  # warning type, message
    
    def __init__(self, exchange_name="htx", demo_mode=True):
        super().__init__()

        # Initialize components
        self.trading_engine = CCXTTradingEngine(exchange_name, demo_mode)
        self.position_tracker = PositionTracker()

        # 🔧 FIX 1: Expose the CCXT exchange object for direct access
        # This fixes the "no attribute 'exchange'" error
        self.exchange = self.trading_engine.exchange
        print(f"✅ RealTradingInterface: Exchange object exposed for direct CCXT access")

        # Trading state
        self.is_trading_enabled = True
        self.current_symbol = "DOGE/USDT:USDT"
        self.current_leverage = 20

        # Connect signals
        self.connect_signals()

        # Status update timer
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(2000)  # Update every 2 seconds
    
    def connect_signals(self):
        """Connect internal component signals"""
        # Trading engine signals
        self.trading_engine.order_placed.connect(self.on_order_placed)
        self.trading_engine.order_filled.connect(self.on_order_filled)
        self.trading_engine.order_cancelled.connect(self.on_order_cancelled)
        self.trading_engine.position_updated.connect(self.on_position_updated)
        self.trading_engine.balance_updated.connect(self.on_balance_updated)
        self.trading_engine.error_occurred.connect(self.on_trading_error)
        self.trading_engine.status_updated.connect(self.on_trading_status)
        
        # Position tracker signals
        self.position_tracker.position_updated.connect(self.on_position_tracker_update)
        self.position_tracker.trade_executed.connect(self.on_trade_executed)
        self.position_tracker.pnl_updated.connect(self.on_pnl_updated)
        self.position_tracker.margin_warning.connect(self.on_margin_warning)

    def get_open_positions(self) -> List[Dict[str, Any]]:
        """Get all open positions for LLM executor compatibility"""
        try:
            # First try to get positions from the exchange
            if hasattr(self.trading_engine, 'get_positions'):
                exchange_positions = self.trading_engine.get_positions()
                if exchange_positions:
                    return exchange_positions

            # Fallback to position tracker
            return self.position_tracker.get_open_positions()

        except Exception as e:
            logger.error(f"Error getting open positions: {e}")
            return []

    # 🚨 ENHANCED: Helper method for minimum order size validation and auto-adjustment
    def validate_and_adjust_minimum_order_size(self, symbol: str, amount: float) -> tuple[bool, float]:
        """Validate and automatically adjust order amount to meet exchange minimum requirements"""
        try:
            # Check if exchange and markets data are available
            if not self.exchange or not hasattr(self.exchange, 'markets'):
                print(f"⚠️ Exchange markets data not available for validation")
                return True, amount  # Allow order if we can't validate

            # Get market info for the symbol
            if symbol not in self.exchange.markets:
                print(f"⚠️ Symbol {symbol} not found in exchange markets")
                return True, amount  # Allow order if symbol not found

            market = self.exchange.markets[symbol]
            limits = market.get('limits', {})
            amount_limits = limits.get('amount', {})
            min_amount = amount_limits.get('min')

            if min_amount is not None and amount < min_amount:
                print(f"🔧 AUTO-ADJUSTING: Order amount {amount:.8f} below minimum {min_amount:.8f} for {symbol}")

                # 🚨 HTX EXCHANGE FIX: Handle contract format for DOGE
                if 'DOGE' in symbol:
                    # HTX DOGE minimum is 1 contract (100 DOGE), not 100 contracts
                    adjusted_amount = max(1.0, min_amount / 100.0)
                    print(f"🔧 HTX DOGE FIX: Minimum {min_amount} DOGE = {adjusted_amount} contracts")
                else:
                    adjusted_amount = min_amount

                print(f"🔧 AUTO-ADJUSTING: Setting order amount to: {adjusted_amount:.8f}")
                self.trading_status.emit(f"Auto-adjusted order size from {amount:.2f} to {adjusted_amount:.2f} (HTX contract format)")
                return True, adjusted_amount

            print(f"✅ Order amount {amount:.8f} meets minimum requirements for {symbol}")
            return True, amount

        except Exception as e:
            print(f"⚠️ Error validating minimum order size: {e}")
            return True, amount  # Allow order if validation fails

    # 🚨 HTX CONTRACT FORMAT: Centralized conversion method
    def convert_to_htx_contract_format(self, symbol: str, amount: float) -> float:
        """Convert DOGE quantities to HTX contract format (1 contract = 100 DOGE)"""
        try:
            if 'DOGE' in symbol and hasattr(self.trading_engine, 'exchange_name') and self.trading_engine.exchange_name == "htx":
                contracts = max(1.0, amount / 100.0)  # Minimum 1 contract
                logger.info(f"🔧 HTX CONTRACT CONVERSION: {amount:.8f} DOGE → {contracts:.8f} contracts")
                print(f"🔧 HTX CONTRACT CONVERSION: {amount:.8f} DOGE → {contracts:.8f} contracts")
                self.trading_status.emit(f"HTX conversion: {amount:.2f} DOGE → {contracts:.2f} contracts")
                return contracts

            return amount

        except Exception as e:
            logger.error(f"Error in HTX contract conversion: {e}")
            return amount

    # 🔧 LEGACY: Keep old method for backward compatibility
    def validate_minimum_order_size(self, symbol: str, amount: float) -> bool:
        """Legacy method - validate if order amount meets exchange minimum requirements"""
        valid, _ = self.validate_and_adjust_minimum_order_size(symbol, amount)
        return valid

    # Public API methods for GUI
    def place_limit_long(self, symbol: str, amount: float, leverage: int = None) -> bool:
        """Place a limit long order using best bid price"""
        try:
            if not self.is_trading_enabled:
                self.trading_error.emit("Trading is disabled")
                return False

            # Auto-adjust minimum order size
            valid, adjusted_amount = self.validate_and_adjust_minimum_order_size(symbol, amount)
            if not valid:
                return False
            amount = adjusted_amount

            # HTX Contract Conversion
            amount = self.convert_to_htx_contract_format(symbol, amount)

            # Set leverage if provided
            if leverage:
                self.set_leverage(symbol, leverage)

            # Get best bid price
            orderbook = self.trading_engine.get_orderbook(symbol)
            if not orderbook or not orderbook.get('bids'):
                self.trading_error.emit(f"Could not get order book for {symbol}")
                return False

            best_bid = orderbook['bids'][0][0]

            # Place order
            order = self.trading_engine.place_limit_order(symbol, 'buy', amount, best_bid)
            return order is not None

        except Exception as e:
            self.trading_error.emit(f"Error placing limit long: {str(e)}")
            return False
    
    def place_limit_short(self, symbol: str, amount: float, leverage: int = None) -> bool:
        """Place a limit short order using best ask price"""
        try:
            if not self.is_trading_enabled:
                self.trading_error.emit("Trading is disabled")
                return False

            # Auto-adjust minimum order size
            valid, adjusted_amount = self.validate_and_adjust_minimum_order_size(symbol, amount)
            if not valid:
                return False
            amount = adjusted_amount

            # HTX Contract Conversion
            amount = self.convert_to_htx_contract_format(symbol, amount)

            # Set leverage if provided
            if leverage:
                self.set_leverage(symbol, leverage)

            # Get best ask price
            orderbook = self.trading_engine.get_orderbook(symbol)
            if not orderbook or not orderbook.get('asks'):
                self.trading_error.emit(f"Could not get order book for {symbol}")
                return False

            best_ask = orderbook['asks'][0][0]

            # Place order
            order = self.trading_engine.place_limit_order(symbol, 'sell', amount, best_ask)
            return order is not None

        except Exception as e:
            self.trading_error.emit(f"Error placing limit short: {str(e)}")
            return False
    
    def place_market_long(self, symbol: str, amount: float, leverage: int = None) -> bool:
        """Place an aggressive limit long order (market orders are prohibited)"""
        try:
            if not self.is_trading_enabled:
                self.trading_error.emit("Trading is disabled")
                return False

            # 🚨 ENHANCED: Auto-adjust minimum order size before placing order
            valid, adjusted_amount = self.validate_and_adjust_minimum_order_size(symbol, amount)
            if not valid:
                return False
            amount = adjusted_amount  # Use the adjusted amount

            # 🚨 HTX CONTRACT FORMAT: Convert to contract format for HTX
            amount = self.convert_to_htx_contract_format(symbol, amount)

            # Set leverage if provided
            if leverage:
                self.set_leverage(symbol, leverage)

            # 🚨 CRITICAL FIX: Use aggressive limit order instead of market order
            self.trading_status.emit("🎯 Using aggressive limit order (market orders prohibited)")

            # Fetch best ask price for aggressive limit order (buy at ask for immediate execution)
            orderbook = self.trading_engine.get_orderbook(symbol)
            if not orderbook or not orderbook.get('asks'):
                self.trading_error.emit(f"Could not get order book for {symbol}")
                return False

            best_ask = orderbook['asks'][0][0]
            # Use best ask price for immediate execution (aggressive limit order)
            aggressive_price = best_ask
            self.trading_status.emit(f"🎯 Placing aggressive limit BUY at {aggressive_price:.6f}")

            try:
                limit_order = self.trading_engine.place_limit_order(symbol, 'buy', amount, aggressive_price)
                if limit_order:
                    self.trading_status.emit(f"✅ Aggressive limit order placed successfully at {aggressive_price:.6f}")
                    return True
                else:
                    self.trading_error.emit("Failed to place aggressive limit order")
                    return False
            except Exception as limit_error:
                self.trading_error.emit(f"Error placing aggressive limit order: {limit_error}")
                return False

        except Exception as e:
            self.trading_error.emit(f"Error placing market long: {str(e)}")
            return False
    
    def place_market_short(self, symbol: str, amount: float, leverage: int = None) -> bool:
        """Place an aggressive limit short order (market orders are prohibited)"""
        try:
            if not self.is_trading_enabled:
                self.trading_error.emit("Trading is disabled")
                return False

            # � ENHANCED: Auto-adjust minimum order size before placing order
            valid, adjusted_amount = self.validate_and_adjust_minimum_order_size(symbol, amount)
            if not valid:
                return False
            amount = adjusted_amount  # Use the adjusted amount

            # 🚨 HTX CONTRACT FORMAT: Convert to contract format for HTX
            amount = self.convert_to_htx_contract_format(symbol, amount)

            # Set leverage if provided
            if leverage:
                self.set_leverage(symbol, leverage)

            # 🚨 CRITICAL FIX: Use aggressive limit order instead of market order
            self.trading_status.emit("🎯 Using aggressive limit order (market orders prohibited)")

            # Fetch best bid price for aggressive limit order (sell at bid for immediate execution)
            orderbook = self.trading_engine.get_orderbook(symbol)
            if not orderbook or not orderbook.get('bids'):
                self.trading_error.emit(f"Could not get order book for {symbol}")
                return False

            best_bid = orderbook['bids'][0][0]
            # Use best bid price for immediate execution (aggressive limit order)
            aggressive_price = best_bid
            self.trading_status.emit(f"🎯 Placing aggressive limit SELL at {aggressive_price:.6f}")

            try:
                limit_order = self.trading_engine.place_limit_order(symbol, 'sell', amount, aggressive_price)
                if limit_order:
                    self.trading_status.emit(f"✅ Aggressive limit order placed successfully at {aggressive_price:.6f}")
                    return True
                else:
                    self.trading_error.emit("Failed to place aggressive limit order")
                    return False
            except Exception as limit_error:
                self.trading_error.emit(f"Error placing aggressive limit order: {limit_error}")
                return False

        except Exception as e:
            self.trading_error.emit(f"Error placing market short: {str(e)}")
            return False
    
    def close_position(self, symbol: str) -> bool:
        """Close a position for the given symbol"""
        try:
            return self.trading_engine.close_position(symbol)
        except Exception as e:
            self.trading_error.emit(f"Error closing position: {str(e)}")
            return False
    
    def close_all_positions(self) -> int:
        """Close all open positions"""
        try:
            return self.trading_engine.close_all_positions()
        except Exception as e:
            self.trading_error.emit(f"Error closing all positions: {str(e)}")
            return 0
    
    def cancel_all_orders(self, symbol: str = None) -> int:
        """Cancel all orders"""
        try:
            return self.trading_engine.cancel_all_orders(symbol)
        except Exception as e:
            self.trading_error.emit(f"Error cancelling orders: {str(e)}")
            return 0
    
    def set_leverage(self, symbol: str, leverage: int) -> bool:
        """Set leverage for a symbol"""
        try:
            return self.trading_engine.set_leverage(symbol, leverage)
        except Exception as e:
            self.trading_error.emit(f"Error setting leverage: {str(e)}")
            return False
    
    # Data access methods
    def get_best_bid_ask(self, symbol: str) -> Tuple[Optional[float], Optional[float]]:
        """Get best bid and ask prices"""
        try:
            orderbook = self.trading_engine.get_orderbook(symbol)
            if not orderbook:
                return None, None
            
            best_bid = orderbook['bids'][0][0] if orderbook.get('bids') else None
            best_ask = orderbook['asks'][0][0] if orderbook.get('asks') else None
            
            return best_bid, best_ask
            
        except Exception as e:
            self.trading_error.emit(f"Error getting bid/ask: {str(e)}")
            return None, None
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price"""
        try:
            ticker = self.trading_engine.get_ticker(symbol)
            return ticker['last'] if ticker else None
        except Exception as e:
            self.trading_error.emit(f"Error getting price: {str(e)}")
            return None
    
    def get_position_info(self, symbol: str) -> Optional[Dict]:
        """Get position information"""
        return self.position_tracker.get_position(symbol)
    
    def get_all_positions(self) -> Dict[str, Dict]:
        """Get all positions"""
        return self.position_tracker.get_all_positions()
    
    def get_open_positions(self) -> Dict[str, Dict]:
        """Get only open positions"""
        return self.position_tracker.get_open_positions()
    
    def get_balance_info(self) -> Optional[Dict]:
        """Get account balance"""
        try:
            return self.trading_engine.get_balance()
        except Exception as e:
            self.trading_error.emit(f"Error getting balance: {str(e)}")
            return None
    
    def get_pnl_summary(self) -> Dict:
        """Get PnL summary"""
        return self.position_tracker.get_pnl_summary()
    
    def get_trade_history(self, symbol: str = None, limit: int = 100) -> List[Dict]:
        """Get trade history"""
        return self.position_tracker.get_trade_history(symbol, limit)
    
    def get_exchange_info(self) -> Dict:
        """Get exchange information"""
        return self.trading_engine.get_exchange_info()

    def fetch_ohlcv(self, symbol: str, timeframe: str = '1m', limit: int = 100) -> Optional[List]:
        """
        Fetch OHLCV data for market analysis

        Args:
            symbol: Trading symbol (e.g., 'BTC/USDT:USDT')
            timeframe: Timeframe ('1m', '5m', '1h', etc.)
            limit: Number of candles to fetch

        Returns:
            List of OHLCV data or None if error
        """
        try:
            if not self.trading_engine or not self.trading_engine.exchange:
                self.trading_error.emit("Exchange not available for OHLCV data")
                return None

            # Fetch OHLCV data from exchange
            ohlcv_data = self.trading_engine.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)

            if ohlcv_data and len(ohlcv_data) > 0:
                self.trading_status.emit(f"Fetched {len(ohlcv_data)} {timeframe} candles for {symbol}")
                return ohlcv_data
            else:
                self.trading_error.emit(f"No OHLCV data available for {symbol}")
                return None

        except Exception as e:
            error_msg = f"Error fetching OHLCV data for {symbol}: {str(e)}"
            self.trading_error.emit(error_msg)
            return None

    def get_orderbook(self, symbol: str) -> Optional[Dict]:
        """
        Get order book data for a symbol

        Args:
            symbol: Trading symbol

        Returns:
            Order book data or None if error
        """
        try:
            if not self.trading_engine:
                return None

            return self.trading_engine.get_orderbook(symbol)

        except Exception as e:
            self.trading_error.emit(f"Error getting orderbook for {symbol}: {str(e)}")
            return None

    def place_limit_order(self, symbol: str, side: str, amount: float, price: float, leverage: int = None) -> Optional[Dict]:
        """
        Place a limit order

        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Order amount
            price: Limit price
            leverage: Optional leverage

        Returns:
            Order result or None if error
        """
        try:
            if not self.is_trading_enabled:
                self.trading_error.emit("Trading is disabled")
                return None

            # Set leverage if provided
            if leverage:
                self.set_leverage(symbol, leverage)

            # Place limit order
            order = self.trading_engine.place_limit_order(symbol, side, amount, price)

            if order:
                self.trading_status.emit(f"Limit {side} order placed: {amount} {symbol} @ {price}")
                return order
            else:
                self.trading_error.emit(f"Failed to place limit {side} order")
                return None

        except Exception as e:
            error_msg = f"Error placing limit {side} order: {str(e)}"
            self.trading_error.emit(error_msg)
            return None

    # Control methods
    def enable_trading(self):
        """Enable trading"""
        self.is_trading_enabled = True
        self.trading_status.emit("Trading enabled")
    
    def disable_trading(self):
        """Disable trading"""
        self.is_trading_enabled = False
        self.trading_status.emit("Trading disabled")
    
    def is_demo_mode(self) -> bool:
        """Check if in demo mode"""
        return self.trading_engine.is_demo_mode()
    
    def is_connected(self) -> bool:
        """Check connection status"""
        return self.trading_engine.get_connection_status()
    
    # Signal handlers
    def on_order_placed(self, order: Dict):
        """Handle order placed signal"""
        self.order_status_updated.emit({
            'type': 'order_placed',
            'order': order
        })
    
    def on_order_filled(self, order: Dict):
        """Handle order filled signal"""
        # Add trade to position tracker
        self.position_tracker.add_trade(order)
        
        self.order_status_updated.emit({
            'type': 'order_filled',
            'order': order
        })
    
    def on_order_cancelled(self, order: Dict):
        """Handle order cancelled signal"""
        self.order_status_updated.emit({
            'type': 'order_cancelled',
            'order': order
        })
    
    def on_position_updated(self, position: Dict):
        """Handle position updated from trading engine"""
        # Update position tracker with new mark price
        if 'markPrice' in position:
            self.position_tracker.update_mark_price(
                position['symbol'], 
                position['markPrice']
            )
    
    def on_balance_updated(self, balance: Dict):
        """Handle balance updated signal"""
        self.balance_status_updated.emit(balance)
    
    def on_trading_error(self, error: str):
        """Handle trading error signal"""
        self.trading_error.emit(error)
    
    def on_trading_status(self, status: str):
        """Handle trading status signal"""
        self.trading_status.emit(status)
    
    def on_position_tracker_update(self, position: Dict):
        """Handle position tracker update"""
        self.position_status_updated.emit(position)
    
    def on_trade_executed(self, trade: Dict):
        """Handle trade executed signal"""
        self.trading_status.emit(f"Trade executed: {trade['side']} {trade['amount']} {trade['symbol']}")
    
    def on_pnl_updated(self, unrealized_pnl: float, realized_pnl: float):
        """Handle PnL updated signal"""
        pnl_summary = {
            'unrealized_pnl': unrealized_pnl,
            'realized_pnl': realized_pnl,
            'total_pnl': unrealized_pnl + realized_pnl
        }
        self.pnl_updated.emit(pnl_summary)
    
    def on_margin_warning(self, warning: str, margin_ratio: float):
        """Handle margin warning signal"""
        self.risk_warning.emit('margin', f"{warning} (Ratio: {margin_ratio:.2%})")
    
    def update_status(self):
        """Update overall status (called by timer)"""
        try:
            # Update current symbol price in position tracker
            if self.current_symbol:
                price = self.get_current_price(self.current_symbol)
                if price:
                    self.position_tracker.update_mark_price(self.current_symbol, price)
                    
        except Exception as e:
            # Don't emit errors for routine updates
            pass
    
    def set_current_symbol(self, symbol: str):
        """Set the current trading symbol"""
        self.current_symbol = symbol
    
    def set_current_leverage(self, leverage: int):
        """Set the current leverage"""
        self.current_leverage = leverage

    def disconnect(self):
        """Disconnect from trading interface and clean up resources"""
        try:
            # Stop status timer
            if hasattr(self, 'status_timer') and self.status_timer:
                self.status_timer.stop()

            # Disable trading
            self.is_trading_enabled = False

            # Disconnect trading engine if it has a disconnect method
            if hasattr(self.trading_engine, 'disconnect'):
                self.trading_engine.disconnect()

            # Clear position tracker
            if hasattr(self.position_tracker, 'clear'):
                self.position_tracker.clear()

            self.trading_status.emit("Disconnected from trading interface")
            print("✅ RealTradingInterface: Disconnected successfully")

        except Exception as e:
            error_msg = f"Error during disconnect: {str(e)}"
            self.trading_error.emit(error_msg)
            print(f"❌ RealTradingInterface disconnect error: {error_msg}")

    def is_connected(self) -> bool:
        """Check if trading interface is connected and operational"""
        try:
            # Check if trading is enabled
            if not self.is_trading_enabled:
                return False

            # Check if trading engine is available
            if not hasattr(self, 'trading_engine') or not self.trading_engine:
                return False

            # Try to get balance as a connection test
            balance = self.get_balance_info()
            return balance is not None

        except Exception:
            return False

    def get_open_positions(self) -> List[Dict[str, Any]]:
        """🚨 CRITICAL FIX: Get all open positions from position tracker"""
        try:
            if hasattr(self, 'position_tracker') and self.position_tracker:
                return self.position_tracker.get_open_positions()
            else:
                logger.warning("Position tracker not available - returning empty positions list")
                return []
        except Exception as e:
            logger.error(f"Error getting open positions: {e}")
            return []
