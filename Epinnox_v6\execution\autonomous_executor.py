"""
Autonomous Trade Executor
Core component that executes trades autonomously based on AI decisions
"""

import asyncio
import time
import logging
from typing import Dict, Optional, List
from dataclasses import dataclass
from enum import Enum
from config.autonomous_trading_rules import AutonomousTradingRules

logger = logging.getLogger(__name__)

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"

@dataclass
class TradeOrder:
    symbol: str
    side: str  # 'buy' or 'sell'
    amount: float
    price: Optional[float] = None
    order_type: OrderType = OrderType.LIMIT  # CRITICAL: LIMIT ORDERS ONLY
    leverage: float = 1.0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None

class AutonomousTradeExecutor:
    """
    Autonomous trade executor that makes real trading decisions and executes them
    """

    def __init__(self, exchange, risk_manager=None, min_confidence=0.7, portfolio_manager=None):
        self.exchange = exchange
        self.risk_manager = risk_manager
        self.portfolio_manager = portfolio_manager
        self.min_confidence = min_confidence
        self.active_orders = {}
        self.position_tracker = {}

        # CRITICAL: Emergency stop state
        self.emergency_stop_active = False
        self.emergency_stop_reason = None
        
    async def execute_trading_decision(self, decision_data: dict) -> dict:
        """
        Execute trading decision autonomously

        Args:
            decision_data: Dict containing:
                - decision: 'LONG', 'SHORT', 'WAIT'
                - confidence: 0-100
                - symbol: trading symbol
                - leverage_position_sizing: position sizing data

        Returns:
            Dict with execution results
        """
        try:
            # CRITICAL: Check emergency stop first
            if self.emergency_stop_active:
                logger.error(f"🚨 EMERGENCY STOP ACTIVE: {self.emergency_stop_reason}")
                return {
                    'status': 'EMERGENCY_STOP',
                    'reason': f'Emergency stop active: {self.emergency_stop_reason}',
                    'action': 'BLOCKED'
                }

            decision = decision_data.get('decision', 'WAIT')
            confidence = decision_data.get('confidence', 0) / 100
            symbol = decision_data.get('selected_symbol', 'DOGE/USDT')

            # AUTONOMOUS DECISION: Only trade if confidence is high enough
            if confidence < self.min_confidence:
                return {
                    'status': 'SKIPPED',
                    'reason': f'Confidence {confidence:.1%} below threshold {self.min_confidence:.1%}',
                    'action': 'WAIT'
                }
                
            if decision == 'WAIT':
                return {'status': 'WAIT', 'reason': 'System decided to wait'}
                
            # Get position sizing data
            position_data = decision_data.get('leverage_position_sizing', {})
            if not position_data:
                return {'status': 'ERROR', 'reason': 'No position sizing data'}
                
            # Pre-execution risk checks
            risk_check = await self.pre_execution_risk_check(symbol, position_data, confidence)
            if not risk_check['approved']:
                return {
                    'status': 'RISK_REJECTED',
                    'reason': risk_check['reason'],
                    'risk_warnings': risk_check.get('warnings', [])
                }
                
            # Get current market price for LIMIT order
            current_price = await self.get_current_price(symbol)
            if not current_price:
                return {'status': 'ERROR', 'reason': 'Could not get current price for LIMIT order'}

            # Calculate LIMIT order price with small buffer for execution
            side = 'buy' if decision == 'LONG' else 'sell'
            limit_price = self.calculate_limit_price(current_price, side)

            # Create trade order - ENFORCED LIMIT ORDERS ONLY
            trade_order = TradeOrder(
                symbol=symbol,
                side=side,
                amount=position_data.get('position_units', 0),
                price=limit_price,  # CRITICAL: Always set price for LIMIT orders
                order_type=OrderType.LIMIT,  # CRITICAL: Explicitly enforce LIMIT
                leverage=position_data.get('effective_leverage', 1.0),
                stop_loss=position_data.get('stop_loss_price'),
                take_profit=position_data.get('take_profit_price')
            )

            # CRITICAL: Validate LIMIT order enforcement
            order_validation = AutonomousTradingRules.validate_order_type(trade_order.order_type.value)
            if not order_validation['valid']:
                logger.error(f"🚨 ORDER REJECTED: {order_validation['reason']}")
                return {
                    'status': 'RULE_VIOLATION',
                    'reason': order_validation['reason'],
                    'rule': order_validation.get('rule'),
                    'severity': order_validation.get('severity')
                }
            
            # Execute the trade
            execution_result = await self.execute_order(trade_order)
            
            # Set up risk management orders (stop loss, take profit)
            if execution_result['status'] == 'FILLED':
                await self.setup_risk_orders(trade_order, execution_result)
                
            return execution_result
            
        except Exception as e:
            logger.error(f"Execution error: {e}")
            return {'status': 'ERROR', 'reason': f'Execution error: {str(e)}'}
    
    async def pre_execution_risk_check(self, symbol: str, position_data: dict, confidence: float) -> dict:
        """Perform comprehensive risk checks before execution"""
        warnings = []

        # Check 1: Position size vs account balance
        position_usd = position_data.get('position_usd', 0)
        account_balance = await self.get_account_balance()

        if position_usd > account_balance * 0.1:  # Max 10% per trade
            warnings.append(f'Position size ${position_usd:.2f} exceeds 10% of balance')

        # Check 2: Leverage limits
        leverage = position_data.get('effective_leverage', 1.0)
        if leverage > 10:  # Max 10x leverage
            return {'approved': False, 'reason': f'Leverage {leverage:.1f}x exceeds maximum 10x'}

        # Check 3: Portfolio risk limits (if portfolio manager is available)
        if hasattr(self, 'portfolio_manager') and self.portfolio_manager:
            try:
                # Check if portfolio manager can accept this position
                can_open_result = await self.portfolio_manager.can_open_position(
                    symbol, position_usd, leverage
                )
                if not can_open_result['allowed']:
                    return {
                        'approved': False,
                        'reason': f'Portfolio risk check failed: {can_open_result["reason"]}',
                        'warnings': warnings
                    }
            except Exception as e:
                warnings.append(f'Portfolio risk check error: {str(e)}')

        # Check 4: Market conditions
        if len(warnings) > 2:
            return {'approved': False, 'reason': 'Too many risk warnings', 'warnings': warnings}

        return {'approved': True, 'warnings': warnings}
    
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for symbol"""
        try:
            if asyncio.iscoroutinefunction(self.exchange.fetch_ticker):
                ticker = await self.exchange.fetch_ticker(symbol)
            else:
                ticker = self.exchange.fetch_ticker(symbol)
            return ticker['last']
        except Exception as e:
            logger.error(f"Failed to get current price for {symbol}: {e}")
            return None

    def calculate_limit_price(self, current_price: float, side: str) -> float:
        """
        Calculate LIMIT order price with small buffer for execution

        Args:
            current_price: Current market price
            side: 'buy' or 'sell'

        Returns:
            LIMIT order price with execution buffer
        """
        # Small buffer to ensure execution (0.1% for buy orders, -0.1% for sell orders)
        buffer = 0.001  # 0.1%

        if side == 'buy':
            # For buy orders, set price slightly above market to ensure execution
            return current_price * (1 + buffer)
        else:
            # For sell orders, set price slightly below market to ensure execution
            return current_price * (1 - buffer)

    async def execute_order(self, order: TradeOrder) -> dict:
        """Execute the actual trade order"""
        try:
            # CRITICAL: Final validation that this is a LIMIT order
            if order.order_type != OrderType.LIMIT:
                logger.error(f"🚨 CRITICAL: Non-LIMIT order attempted: {order.order_type}")
                return {
                    'status': 'REJECTED',
                    'reason': f'LIMIT ORDERS ONLY - {order.order_type.value} orders are forbidden',
                    'order': order.__dict__
                }

            # CRITICAL: Ensure price is set for LIMIT orders
            if order.price is None:
                logger.error("🚨 CRITICAL: LIMIT order missing price")
                return {
                    'status': 'REJECTED',
                    'reason': 'LIMIT orders must have a price',
                    'order': order.__dict__
                }

            # Convert to exchange format
            order_params = {
                'symbol': order.symbol,
                'type': order.order_type.value,
                'side': order.side,
                'amount': order.amount,
                'price': order.price,  # CRITICAL: Always include price for LIMIT orders
            }

            # Add leverage for futures
            if ':' in order.symbol:  # Futures symbol
                order_params['leverage'] = order.leverage

            logger.info(f"🎯 Executing LIMIT order: {order.side} {order.amount} {order.symbol} @ {order.price}")

            # Execute order (handle both sync and async exchanges)
            if asyncio.iscoroutinefunction(self.exchange.create_order):
                result = await self.exchange.create_order(**order_params)
            else:
                result = self.exchange.create_order(**order_params)
            
            # Track the order
            self.active_orders[result['id']] = {
                'order': order,
                'result': result,
                'timestamp': time.time()
            }
            
            return {
                'status': 'FILLED',
                'order_id': result['id'],
                'symbol': order.symbol,
                'side': order.side,
                'amount': result['filled'],
                'price': result['average'],
                'fee': result.get('fee', {}),
                'timestamp': result['timestamp']
            }
            
        except Exception as e:
            logger.error(f"Order execution failed: {e}")
            return {
                'status': 'FAILED',
                'reason': str(e),
                'order': order.__dict__
            }
    
    async def setup_risk_orders(self, original_order: TradeOrder, execution_result: dict):
        """
        Set up stop loss and take profit orders
        NOTE: Stop loss and take profit orders are acceptable risk management tools
        even though they are not pure LIMIT orders
        """
        if not (original_order.stop_loss or original_order.take_profit):
            return

        try:
            # Stop Loss Order - Risk management exception to LIMIT-only rule
            if original_order.stop_loss:
                logger.info(f"🛡️ Setting up stop loss at {original_order.stop_loss}")
                stop_order = {
                    'symbol': original_order.symbol,
                    'type': 'stop_loss',
                    'side': 'sell' if original_order.side == 'buy' else 'buy',
                    'amount': execution_result['amount'],
                    'stopPrice': original_order.stop_loss
                }
                if asyncio.iscoroutinefunction(self.exchange.create_order):
                    await self.exchange.create_order(**stop_order)
                else:
                    self.exchange.create_order(**stop_order)

            # Take Profit Order - Risk management exception to LIMIT-only rule
            if original_order.take_profit:
                logger.info(f"🎯 Setting up take profit at {original_order.take_profit}")
                tp_order = {
                    'symbol': original_order.symbol,
                    'type': 'take_profit',
                    'side': 'sell' if original_order.side == 'buy' else 'buy',
                    'amount': execution_result['amount'],
                    'stopPrice': original_order.take_profit
                }
                if asyncio.iscoroutinefunction(self.exchange.create_order):
                    await self.exchange.create_order(**tp_order)
                else:
                    self.exchange.create_order(**tp_order)

        except Exception as e:
            logger.error(f"Failed to set up risk orders: {e}")
    
    async def get_account_balance(self) -> float:
        """Get account balance - implement based on your exchange"""
        try:
            if asyncio.iscoroutinefunction(self.exchange.fetch_balance):
                balance = await self.exchange.fetch_balance()
            else:
                balance = self.exchange.fetch_balance()

            # Extract USDT balance properly
            usdt_balance = balance.get('USDT', {}).get('free', 0)
            logger.info(f"🔍 Retrieved account balance: ${usdt_balance:.2f}")
            return usdt_balance
        except Exception as e:
            logger.error(f"Error fetching account balance: {e}")
            # Return a minimal fallback but log the issue
            logger.warning("⚠️ Using minimal fallback balance due to fetch error")
            return 50.0  # Minimal fallback - should trigger investigation

    async def get_balance(self) -> dict:
        """Get balance for health check compatibility"""
        try:
            balance_amount = await self.get_account_balance()
            return {
                'USDT': {'free': balance_amount, 'used': 0, 'total': balance_amount},
                'total': balance_amount
            }
        except Exception as e:
            logger.error(f"Error getting balance: {e}")
            return {'USDT': {'free': 0, 'used': 0, 'total': 0}, 'total': 0}

    async def cancel_all_orders(self, reason: str = "Emergency stop"):
        """Cancel all active orders"""
        logger.warning(f"[EXECUTOR] Cancelling all orders: {reason}")

        cancelled_count = 0
        orders_to_cancel = list(self.active_orders.keys())  # Create a copy of keys

        for order_id in orders_to_cancel:
            try:
                order_info = self.active_orders[order_id]
                symbol = order_info['order'].symbol

                # Cancel on exchange
                if asyncio.iscoroutinefunction(self.exchange.cancel_order):
                    await self.exchange.cancel_order(order_id, symbol)
                else:
                    self.exchange.cancel_order(order_id, symbol)

                # Remove from tracking
                del self.active_orders[order_id]
                cancelled_count += 1
                logger.info(f"[EXECUTOR] Cancelled order: {order_id}")

            except Exception as e:
                logger.error(f"[EXECUTOR] Failed to cancel order {order_id}: {e}")

        logger.warning(f"[EXECUTOR] Cancelled {cancelled_count} orders due to: {reason}")
        return cancelled_count

    async def emergency_stop(self, reason: str = "Manual emergency stop") -> dict:
        """
        CRITICAL: Emergency stop - immediately halt all trading activities

        Args:
            reason: Reason for emergency stop

        Returns:
            Dict with emergency stop results
        """
        try:
            logger.critical(f"🚨 EMERGENCY STOP ACTIVATED: {reason}")

            # Set emergency stop state
            self.emergency_stop_active = True
            self.emergency_stop_reason = reason

            # Cancel all active orders
            cancelled_orders = await self.cancel_all_orders(f"Emergency stop: {reason}")

            # Close all positions if portfolio manager is available
            closed_positions = 0
            if self.portfolio_manager:
                try:
                    await self.portfolio_manager.close_all_positions(f"Emergency stop: {reason}")
                    closed_positions = len(self.position_tracker)
                except Exception as e:
                    logger.error(f"Failed to close positions during emergency stop: {e}")

            # Clear tracking
            self.position_tracker.clear()

            logger.critical(f"🛑 EMERGENCY STOP COMPLETED: Cancelled {cancelled_orders} orders, closed {closed_positions} positions")

            return {
                'status': 'EMERGENCY_STOP_COMPLETED',
                'reason': reason,
                'cancelled_orders': cancelled_orders,
                'closed_positions': closed_positions,
                'timestamp': time.time()
            }

        except Exception as e:
            logger.error(f"CRITICAL ERROR during emergency stop: {e}")
            return {
                'status': 'EMERGENCY_STOP_FAILED',
                'reason': reason,
                'error': str(e),
                'timestamp': time.time()
            }

    def reset_emergency_stop(self, authorization_code: str = None) -> bool:
        """
        Reset emergency stop (requires authorization)

        Args:
            authorization_code: Authorization code for reset

        Returns:
            bool: True if reset successful
        """
        try:
            # Simple authorization check (in production, use proper authentication)
            if authorization_code != "RESET_EMERGENCY_2024":
                logger.error("🚨 UNAUTHORIZED emergency stop reset attempt")
                return False

            self.emergency_stop_active = False
            self.emergency_stop_reason = None

            logger.warning("⚠️ Emergency stop reset - trading activities can resume")
            return True

        except Exception as e:
            logger.error(f"Error resetting emergency stop: {e}")
            return False
