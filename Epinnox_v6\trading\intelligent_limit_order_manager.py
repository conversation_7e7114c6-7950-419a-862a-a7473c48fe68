#!/usr/bin/env python3
"""
Intelligent Limit Order Manager for Professional Scalping
Replaces market orders with sophisticated limit order management system
"""

import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict
import threading
from PyQt5.QtCore import QObject, pyqtSignal, QTimer

logger = logging.getLogger(__name__)

class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING = "pending"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"

class OrderType(Enum):
    """Order type enumeration"""
    LIMIT_BUY = "limit_buy"
    LIMIT_SELL = "limit_sell"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"

@dataclass
class SmartLimitOrder:
    """Smart limit order with lifecycle management"""
    id: str
    symbol: str
    side: str  # 'buy' or 'sell'
    amount: float
    price: float
    order_type: OrderType
    status: OrderStatus = OrderStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    filled_amount: float = 0.0
    average_fill_price: float = 0.0
    exchange_order_id: Optional[str] = None
    timeout_seconds: int = 60  # Default 60 seconds for scalping
    max_replacements: int = 3
    replacement_count: int = 0
    spread_adjustment_ticks: int = 1  # Ticks to adjust from best bid/ask
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def is_expired(self) -> bool:
        """Check if order has expired"""
        return (datetime.now() - self.created_at).total_seconds() > self.timeout_seconds
    
    @property
    def remaining_amount(self) -> float:
        """Get remaining unfilled amount"""
        return self.amount - self.filled_amount
    
    @property
    def is_active(self) -> bool:
        """Check if order is still active"""
        return self.status in [OrderStatus.PENDING, OrderStatus.PARTIALLY_FILLED]

class IntelligentLimitOrderManager(QObject):
    """
    Professional scalping-oriented limit order management system
    Replaces market orders with intelligent limit order placement and lifecycle management
    """
    
    # Signals for UI updates
    order_placed = pyqtSignal(dict)
    order_filled = pyqtSignal(dict)
    order_cancelled = pyqtSignal(dict)
    order_replaced = pyqtSignal(dict, dict)  # old_order, new_order
    order_expired = pyqtSignal(dict)
    
    def __init__(self, trading_engine, live_data_manager):
        super().__init__()
        
        self.trading_engine = trading_engine
        self.live_data_manager = live_data_manager
        
        # Order management
        self.active_orders: Dict[str, SmartLimitOrder] = {}
        self.order_history: List[SmartLimitOrder] = []
        self.order_lock = threading.RLock()
        
        # 🚨 SCALPING OPTIMIZATION: Enhanced configuration for aggressive scalping
        self.config = {
            'default_timeout_seconds': 30,  # 🚀 Faster timeout for aggressive scalping (reduced from 60)
            'max_spread_pct': 0.3,  # 🚀 Slightly wider spread tolerance for more opportunities (increased from 0.2)
            'min_order_book_depth': 50,  # 🚀 Lower depth requirement for more opportunities (reduced from 100)
            'price_improvement_ticks': 2,  # 🚀 More aggressive price improvement (increased from 1)
            'max_order_replacements': 5,  # 🚀 More order replacements for better fills (increased from 3)
            'order_monitoring_interval_ms': 500,  # 🚀 Faster monitoring for quicker reactions (reduced from 1000)
            'aggressive_fill_mode': True,  # Place orders aggressively within spread
            'liquidity_threshold': 500,  # 🚀 Lower liquidity requirement for more opportunities (reduced from 1000)
        }
        
        # Performance tracking
        self.stats = {
            'orders_placed': 0,
            'orders_filled': 0,
            'orders_cancelled': 0,
            'orders_expired': 0,
            'orders_replaced': 0,
            'average_fill_time': 0.0,
            'fill_rate': 0.0,
        }
        
        # Setup order monitoring timer
        self.monitoring_timer = QTimer()
        self.monitoring_timer.timeout.connect(self.monitor_active_orders)
        self.monitoring_timer.start(self.config['order_monitoring_interval_ms'])
        
        logger.info("Intelligent Limit Order Manager initialized for professional scalping")
    
    def place_smart_limit_order(self, symbol: str, side: str, amount: float, 
                               confidence: float = 85.0, timeout_seconds: int = None) -> Optional[SmartLimitOrder]:
        """
        Place an intelligent limit order using real-time order book analysis
        
        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            amount: Order amount
            confidence: LLM confidence level (affects aggressiveness)
            timeout_seconds: Custom timeout (default from config)
            
        Returns:
            SmartLimitOrder object if successful, None otherwise
        """
        try:
            # Get real-time order book
            order_book = self.live_data_manager.get_latest_orderbook(symbol)
            if not order_book:
                logger.error(f"❌ No order book data available for {symbol}")
                return None

            if not self._validate_order_book(order_book):
                logger.error(f"❌ Order book validation failed for {symbol}")
                # Log order book details for debugging
                bids = order_book.get('bids', [])
                asks = order_book.get('asks', [])
                logger.error(f"❌ Order book details: {len(bids)} bids, {len(asks)} asks")
                if bids and asks:
                    best_bid = float(bids[0][0])
                    best_ask = float(asks[0][0])
                    spread_pct = ((best_ask - best_bid) / best_bid) * 100
                    logger.error(f"❌ Spread: {spread_pct:.3f}%, Best bid: {best_bid}, Best ask: {best_ask}")
                return None
            
            # Calculate optimal limit price
            optimal_price = self._calculate_optimal_limit_price(order_book, side, confidence)
            if optimal_price is None:
                logger.warning(f"Could not calculate optimal price for {symbol} {side}")
                return None
            
            # Create smart limit order
            order_id = f"smart_{side}_{symbol}_{int(time.time() * 1000)}"
            order_type = OrderType.LIMIT_BUY if side == 'buy' else OrderType.LIMIT_SELL
            
            smart_order = SmartLimitOrder(
                id=order_id,
                symbol=symbol,
                side=side,
                amount=amount,
                price=optimal_price,
                order_type=order_type,
                timeout_seconds=timeout_seconds or self.config['default_timeout_seconds'],
                spread_adjustment_ticks=self._calculate_spread_adjustment(confidence),
                metadata={
                    'confidence': confidence,
                    'order_book_snapshot': {
                        'best_bid': order_book['bids'][0][0] if order_book['bids'] else 0,
                        'best_ask': order_book['asks'][0][0] if order_book['asks'] else 0,
                        'spread_pct': self._calculate_spread_pct(order_book)
                    }
                }
            )
            
            # Place order on exchange
            exchange_order = self._place_exchange_order(smart_order)
            if exchange_order:
                smart_order.exchange_order_id = exchange_order.get('id')
                
                # Add to active orders
                with self.order_lock:
                    self.active_orders[order_id] = smart_order
                
                # Update stats
                self.stats['orders_placed'] += 1
                
                # Emit signal
                self.order_placed.emit(self._order_to_dict(smart_order))
                
                logger.info(f"✅ Smart limit order placed: {side.upper()} {amount} {symbol} @ ${optimal_price:.6f}")
                return smart_order
            else:
                logger.error(f"Failed to place exchange order for {symbol}")
                return None
                
        except Exception as e:
            logger.error(f"Error placing smart limit order: {e}")
            return None
    
    def cancel_order(self, order_id: str, reason: str = "manual") -> bool:
        """Cancel an active order"""
        try:
            with self.order_lock:
                if order_id not in self.active_orders:
                    logger.warning(f"Order {order_id} not found in active orders")
                    return False
                
                order = self.active_orders[order_id]
                
                # Cancel on exchange
                if order.exchange_order_id:
                    try:
                        self.trading_engine.cancel_order(order.exchange_order_id, order.symbol)
                        logger.info(f"✅ Cancelled exchange order {order.exchange_order_id}")
                    except Exception as e:
                        logger.warning(f"Failed to cancel exchange order: {e}")
                
                # Update order status
                order.status = OrderStatus.CANCELLED
                order.updated_at = datetime.now()
                order.metadata['cancellation_reason'] = reason
                
                # Move to history
                self.order_history.append(order)
                del self.active_orders[order_id]
                
                # Update stats
                self.stats['orders_cancelled'] += 1
                
                # Emit signal
                self.order_cancelled.emit(self._order_to_dict(order))
                
                return True
                
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}")
            return False
    
    def replace_order(self, order_id: str, new_confidence: float = None) -> Optional[SmartLimitOrder]:
        """Replace an existing order with updated price"""
        try:
            with self.order_lock:
                if order_id not in self.active_orders:
                    return None
                
                old_order = self.active_orders[order_id]
                
                # Check replacement limits
                if old_order.replacement_count >= old_order.max_replacements:
                    logger.warning(f"Order {order_id} has reached max replacements")
                    return None
                
                # Cancel old order
                self.cancel_order(order_id, "replacement")
                
                # Place new order with updated price
                confidence = new_confidence or old_order.metadata.get('confidence', 85.0)
                new_order = self.place_smart_limit_order(
                    old_order.symbol,
                    old_order.side,
                    old_order.remaining_amount,
                    confidence,
                    old_order.timeout_seconds
                )
                
                if new_order:
                    new_order.replacement_count = old_order.replacement_count + 1
                    self.stats['orders_replaced'] += 1
                    
                    # Emit signal
                    self.order_replaced.emit(
                        self._order_to_dict(old_order),
                        self._order_to_dict(new_order)
                    )
                    
                    logger.info(f"✅ Replaced order {order_id} with {new_order.id}")
                
                return new_order
                
        except Exception as e:
            logger.error(f"Error replacing order {order_id}: {e}")
            return None

    def monitor_active_orders(self):
        """Monitor active orders for fills, expirations, and replacement opportunities"""
        try:
            with self.order_lock:
                orders_to_remove = []
                orders_to_replace = []

                for order_id, order in self.active_orders.items():
                    # Check for expiration
                    if order.is_expired:
                        logger.info(f"⏰ Order {order_id} expired after {order.timeout_seconds}s")
                        order.status = OrderStatus.EXPIRED
                        order.metadata['expiration_reason'] = 'timeout'
                        orders_to_remove.append(order_id)
                        self.stats['orders_expired'] += 1
                        self.order_expired.emit(self._order_to_dict(order))
                        continue

                    # Check order status on exchange
                    if order.exchange_order_id:
                        exchange_status = self._check_exchange_order_status(order)
                        if exchange_status:
                            self._update_order_from_exchange(order, exchange_status)

                            # If filled, remove from active orders
                            if order.status == OrderStatus.FILLED:
                                orders_to_remove.append(order_id)
                                self.stats['orders_filled'] += 1
                                self.order_filled.emit(self._order_to_dict(order))
                                continue

                    # Check if order needs replacement due to market movement
                    if self._should_replace_order(order):
                        orders_to_replace.append(order_id)

                # Remove completed/expired orders
                for order_id in orders_to_remove:
                    if order_id in self.active_orders:
                        self.order_history.append(self.active_orders[order_id])
                        del self.active_orders[order_id]

                # Replace orders that need updating
                for order_id in orders_to_replace:
                    if order_id in self.active_orders:
                        self.replace_order(order_id)

        except Exception as e:
            logger.error(f"Error monitoring active orders: {e}")

    def cancel_all_orders(self, symbol: str = None, reason: str = "bulk_cancel") -> int:
        """Cancel all active orders, optionally filtered by symbol"""
        cancelled_count = 0

        try:
            with self.order_lock:
                orders_to_cancel = []

                for order_id, order in self.active_orders.items():
                    if symbol is None or order.symbol == symbol:
                        orders_to_cancel.append(order_id)

                for order_id in orders_to_cancel:
                    if self.cancel_order(order_id, reason):
                        cancelled_count += 1

            logger.info(f"✅ Cancelled {cancelled_count} orders (reason: {reason})")
            return cancelled_count

        except Exception as e:
            logger.error(f"Error cancelling all orders: {e}")
            return cancelled_count

    def get_active_orders(self, symbol: str = None) -> List[SmartLimitOrder]:
        """Get list of active orders, optionally filtered by symbol"""
        try:
            with self.order_lock:
                if symbol:
                    return [order for order in self.active_orders.values() if order.symbol == symbol]
                else:
                    return list(self.active_orders.values())
        except Exception as e:
            logger.error(f"Error getting active orders: {e}")
            return []

    def get_order_stats(self) -> Dict[str, Any]:
        """Get order management statistics"""
        try:
            total_orders = self.stats['orders_placed']
            if total_orders > 0:
                self.stats['fill_rate'] = (self.stats['orders_filled'] / total_orders) * 100

            return {
                **self.stats,
                'active_orders_count': len(self.active_orders),
                'total_orders_history': len(self.order_history)
            }
        except Exception as e:
            logger.error(f"Error getting order stats: {e}")
            return {}

    def _validate_order_book(self, order_book: Dict) -> bool:
        """Validate order book quality for limit order placement"""
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])

            # Check basic structure
            if not bids or not asks:
                return False

            # Check spread
            best_bid = float(bids[0][0])
            best_ask = float(asks[0][0])
            spread_pct = ((best_ask - best_bid) / best_bid) * 100

            if spread_pct > self.config['max_spread_pct']:
                logger.warning(f"Spread too wide: {spread_pct:.3f}% > {self.config['max_spread_pct']}%")
                return False

            # Check liquidity depth
            bid_depth = sum(float(bid[1]) for bid in bids[:5])
            ask_depth = sum(float(ask[1]) for ask in asks[:5])

            if bid_depth < self.config['liquidity_threshold'] or ask_depth < self.config['liquidity_threshold']:
                logger.warning(f"Insufficient liquidity: bid={bid_depth:.0f}, ask={ask_depth:.0f}")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating order book: {e}")
            return False

    def _calculate_optimal_limit_price(self, order_book: Dict, side: str, confidence: float) -> Optional[float]:
        """Calculate optimal limit price based on order book and confidence"""
        try:
            bids = order_book['bids']
            asks = order_book['asks']

            if side == 'buy':
                # For buy orders, start from best bid and adjust based on confidence
                best_bid = float(bids[0][0])

                if confidence >= 90:
                    # High confidence: aggressive pricing (jump into the pool)
                    return best_bid + (best_bid * 0.0001)  # Slightly above best bid
                elif confidence >= 80:
                    # Medium confidence: at best bid
                    return best_bid
                else:
                    # Low confidence: conservative pricing
                    return best_bid - (best_bid * 0.0001)  # Slightly below best bid

            else:  # sell
                # For sell orders, start from best ask and adjust based on confidence
                best_ask = float(asks[0][0])

                if confidence >= 90:
                    # High confidence: aggressive pricing (jump into the pool)
                    return best_ask - (best_ask * 0.0001)  # Slightly below best ask
                elif confidence >= 80:
                    # Medium confidence: at best ask
                    return best_ask
                else:
                    # Low confidence: conservative pricing
                    return best_ask + (best_ask * 0.0001)  # Slightly above best ask

        except Exception as e:
            logger.error(f"Error calculating optimal limit price: {e}")
            return None

    def _calculate_spread_adjustment(self, confidence: float) -> int:
        """Calculate spread adjustment ticks based on confidence"""
        if confidence >= 90:
            return 2  # More aggressive
        elif confidence >= 80:
            return 1  # Standard
        else:
            return 0  # Conservative

    def _calculate_spread_pct(self, order_book: Dict) -> float:
        """Calculate spread percentage"""
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])

            if not bids or not asks:
                return 100.0  # Invalid spread

            best_bid = float(bids[0][0])
            best_ask = float(asks[0][0])

            return ((best_ask - best_bid) / best_bid) * 100

        except Exception:
            return 100.0

    def _place_exchange_order(self, smart_order: SmartLimitOrder) -> Optional[Dict]:
        """Place order on exchange"""
        try:
            logger.info(f"🎯 Placing exchange order: {smart_order.side} {smart_order.amount} {smart_order.symbol} @ {smart_order.price}")

            # Use trading engine to place limit order
            exchange_order = self.trading_engine.place_limit_order(
                symbol=smart_order.symbol,
                side=smart_order.side,
                amount=smart_order.amount,
                price=smart_order.price,
                params={
                    'offset': 'open',
                    'lever_rate': 20,  # Default leverage
                    'marginMode': 'cross'
                }
            )

            if exchange_order:
                logger.info(f"✅ Exchange order placed successfully: {exchange_order.get('id', 'NO_ID')}")
                return exchange_order
            else:
                logger.error(f"❌ Trading engine returned None for order placement")
                return None

        except Exception as e:
            logger.error(f"❌ Error placing exchange order: {e}")
            logger.error(f"❌ Order details: {smart_order.side} {smart_order.amount} {smart_order.symbol} @ {smart_order.price}")
            return None

    def _check_exchange_order_status(self, order: SmartLimitOrder) -> Optional[Dict]:
        """Check order status on exchange"""
        try:
            if not order.exchange_order_id:
                return None

            # Fetch order status from exchange
            status = self.trading_engine.get_order_status(order.exchange_order_id, order.symbol)
            return status

        except Exception as e:
            logger.error(f"Error checking exchange order status: {e}")
            return None

    def _update_order_from_exchange(self, order: SmartLimitOrder, exchange_status: Dict):
        """Update order status from exchange data"""
        try:
            status = exchange_status.get('status', '').lower()
            filled = float(exchange_status.get('filled', 0))

            # Update filled amount
            if filled > order.filled_amount:
                order.filled_amount = filled
                order.updated_at = datetime.now()

            # Update status
            if status == 'closed' or filled >= order.amount:
                order.status = OrderStatus.FILLED
                if 'average' in exchange_status:
                    order.average_fill_price = float(exchange_status['average'])
            elif filled > 0:
                order.status = OrderStatus.PARTIALLY_FILLED
            elif status == 'canceled':
                order.status = OrderStatus.CANCELLED
            elif status == 'rejected':
                order.status = OrderStatus.REJECTED

        except Exception as e:
            logger.error(f"Error updating order from exchange: {e}")

    def _should_replace_order(self, order: SmartLimitOrder) -> bool:
        """Determine if order should be replaced due to market movement"""
        try:
            # Don't replace if already replaced too many times
            if order.replacement_count >= order.max_replacements:
                return False

            # Don't replace if order is too new (give it time to fill)
            if (datetime.now() - order.updated_at).total_seconds() < 10:
                return False

            # Get current order book
            order_book = self.live_data_manager.get_latest_orderbook(order.symbol)
            if not order_book or not self._validate_order_book(order_book):
                return False

            # Check if our price is still competitive
            bids = order_book['bids']
            asks = order_book['asks']

            if order.side == 'buy':
                best_bid = float(bids[0][0])
                # Replace if our bid is more than 2 ticks behind
                return order.price < best_bid - (best_bid * 0.0002)
            else:
                best_ask = float(asks[0][0])
                # Replace if our ask is more than 2 ticks behind
                return order.price > best_ask + (best_ask * 0.0002)

        except Exception as e:
            logger.error(f"Error checking if order should be replaced: {e}")
            return False

    def _order_to_dict(self, order: SmartLimitOrder) -> Dict[str, Any]:
        """Convert SmartLimitOrder to dictionary for signals"""
        return {
            'id': order.id,
            'symbol': order.symbol,
            'side': order.side,
            'amount': order.amount,
            'price': order.price,
            'status': order.status.value,
            'filled_amount': order.filled_amount,
            'remaining_amount': order.remaining_amount,
            'created_at': order.created_at.isoformat(),
            'updated_at': order.updated_at.isoformat(),
            'exchange_order_id': order.exchange_order_id,
            'replacement_count': order.replacement_count,
            'metadata': order.metadata
        }

    def emergency_cancel_all(self, reason: str = "emergency") -> int:
        """Emergency cancellation of all orders"""
        logger.warning(f"🚨 EMERGENCY: Cancelling all orders - {reason}")
        return self.cancel_all_orders(reason=f"emergency_{reason}")

    def get_order_book_analysis(self, symbol: str) -> Dict[str, Any]:
        """Get detailed order book analysis for decision making"""
        try:
            order_book = self.live_data_manager.get_latest_orderbook(symbol)
            if not order_book:
                return {}

            bids = order_book.get('bids', [])[:5]
            asks = order_book.get('asks', [])[:5]

            if not bids or not asks:
                return {}

            best_bid = float(bids[0][0])
            best_ask = float(asks[0][0])
            spread = best_ask - best_bid
            spread_pct = (spread / best_bid) * 100

            # Calculate depth
            bid_depth = sum(float(bid[1]) for bid in bids)
            ask_depth = sum(float(ask[1]) for ask in asks)

            # Calculate imbalance
            total_depth = bid_depth + ask_depth
            imbalance = (bid_depth - ask_depth) / total_depth if total_depth > 0 else 0

            return {
                'best_bid': best_bid,
                'best_ask': best_ask,
                'spread': spread,
                'spread_pct': spread_pct,
                'bid_depth': bid_depth,
                'ask_depth': ask_depth,
                'imbalance': imbalance,
                'is_tradeable': spread_pct <= self.config['max_spread_pct'],
                'liquidity_sufficient': min(bid_depth, ask_depth) >= self.config['liquidity_threshold']
            }

        except Exception as e:
            logger.error(f"Error analyzing order book: {e}")
            return {}
