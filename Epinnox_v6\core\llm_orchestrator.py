"""
LLM Prompt Orchestrator - Complete AI Trading System
Manages multiple specialized LLM prompts for comprehensive trading decisions
"""

import time
import json
import logging
import asyncio
import concurrent.futures
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import requests
import pandas as pd

from core.vote_aggregator import UnifiedVoteAggregator
from core.dynamic_key_levels import DynamicKeyLevelsCalculator

logger = logging.getLogger(__name__)

ACTION_ZONE_PCT = 0.5
def aggregate_signal(signals, weights):
    score = {}
    for sig, w in zip(signals, weights):
        decision = sig['decision']
        if decision not in score:
            score[decision] = 0.0
        score[decision] += sig['confidence'] * w
    return max(score, key=score.get), score

def price_in_action_zone(price, support, resistance):
    low  = support*(1+ACTION_ZONE_PCT/100)
    high = resistance*(1-ACTION_ZONE_PCT/100)
    return low <= price <= high

def schedule_next(self, decision):
    if self.cache and hasattr(self.cache, 'price_history'):
        ph = [p for _, p in self.cache.price_history]
        move_pct = abs(ph[-1]-ph[-2])/ph[-2]*100 if len(ph)>1 else 100
    else:
        move_pct = 100  # Default to high movement if no cache
        
    if move_pct<0.01:
        interval=120
    elif (self.cache and hasattr(self.cache, 'signal_history') and 
          len(self.cache.signal_history)>=3 and 
          all(s['decision']=='WAIT' for s in list(self.cache.signal_history)[-3:])):
        interval=300
    elif (self.cache and hasattr(self.cache, 'price_history') and 
          len(ph)>0 and 
          price_in_action_zone(ph[-1], self.current_support, self.current_resistance)):
        interval=15
    else:
        interval=60
    self.logger.info(f"Next run in {interval}s (last: {decision}).")
    self.schedule_auto_refresh(interval)

class PromptPriority(Enum):
    EMERGENCY = 1
    CRITICAL = 2
    HIGH = 3
    MEDIUM = 4
    LOW = 5

class PromptType(Enum):
    EMERGENCY_RESPONSE = "emergency_response"
    POSITION_MANAGEMENT = "position_management"
    PROFIT_OPTIMIZATION = "profit_optimization"
    MARKET_REGIME = "market_regime"
    RISK_ASSESSMENT = "risk_assessment"
    ENTRY_TIMING = "entry_timing"
    STRATEGY_ADAPTATION = "strategy_adaptation"
    OPPORTUNITY_SCANNER = "opportunity_scanner"

@dataclass
class PromptResult:
    prompt_type: PromptType
    timestamp: datetime
    response: Dict[str, Any]
    confidence: float
    execution_time: float
    success: bool
    error_message: Optional[str] = None

@dataclass
class TradingContext:
    """Complete trading context for LLM prompts"""
    symbol: str
    current_price: float
    account_balance: float
    open_positions: List[Dict]
    market_data: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    emergency_flags: List[str]
    timestamp: datetime
    recent_prices: List[tuple] = None  # List of (timestamp, price) tuples
    recent_signals: List[Dict] = None  # List of recent signal decisions

    def __post_init__(self):
        """Initialize default values for optional fields"""
        if self.recent_prices is None:
            self.recent_prices = []
        if self.recent_signals is None:
            self.recent_signals = []

    def __getitem__(self, key):
        """Make TradingContext subscriptable like a dictionary"""
        try:
            return getattr(self, key)
        except AttributeError:
            # Return None for missing attributes instead of raising an error
            return None

    def __setitem__(self, key, value):
        """Allow setting attributes like a dictionary"""
        setattr(self, key, value)

    def __contains__(self, key):
        """Support 'in' operator"""
        return hasattr(self, key)

    def get(self, key, default=None):
        """Dictionary-style get method"""
        return getattr(self, key, default)

class LLMPromptOrchestrator:
    """
    Master orchestrator for all LLM trading prompts
    Manages priority, timing, and execution of specialized AI trading decisions
    """
    
    def should_execute_prompt(self, prompt_type: PromptType) -> bool:
        """Determine if a prompt should be executed based on timing and conditions"""
        
        # Check timing interval
        last_execution = self.last_execution_times.get(prompt_type)
        if last_execution:
            time_since_last = time.time() - last_execution
            if time_since_last < self.prompt_intervals[prompt_type]:
                return False
        
        # Emergency prompts always execute if emergency conditions exist
        if prompt_type == PromptType.EMERGENCY_RESPONSE:
            return len(self.emergency_flags) > 0
        
        # Position-related prompts only if positions exist
        if prompt_type in [PromptType.POSITION_MANAGEMENT, PromptType.PROFIT_OPTIMIZATION]:
            return self.has_active_positions()
        
        # Market analysis prompts only if not in emergency mode
        if prompt_type in [PromptType.MARKET_REGIME, PromptType.OPPORTUNITY_SCANNER]:
            return not self.emergency_mode
        
        # Entry timing only if we have capacity for new positions
        if prompt_type == PromptType.ENTRY_TIMING:
            return self.has_position_capacity() and not self.emergency_mode
        
        return True

    def execute_prompt_cycle_parallel(self, trading_context, mode: str = "full") -> Dict[str, PromptResult]:
        """🚀 PARALLEL: Execute LLM prompts in parallel for maximum speed (8s vs 23s)"""

        cycle_results = {}
        start_time = time.time()

        try:
            # Update emergency flags
            self.update_emergency_flags(trading_context)

            # 🚀 PERFORMANCE OPTIMIZATION: Selective prompt execution based on mode
            if mode == "scalping":
                # Fast execution for scalping (3 essential prompts only)
                prompt_sequence = [
                    PromptType.RISK_ASSESSMENT,       # Priority 1: Risk check
                    PromptType.ENTRY_TIMING,          # Priority 2: Entry decisions
                    PromptType.OPPORTUNITY_SCANNER,   # Priority 3: Opportunity identification
                ]
                logger.info("🏃 SCALPING MODE: Executing 3 essential prompts in PARALLEL for maximum speed")
            else:
                # Full analysis mode (all prompts)
                prompt_sequence = [
                    PromptType.EMERGENCY_RESPONSE,    # Priority 1: IMMEDIATE (skip others if action taken)
                    PromptType.MARKET_REGIME,         # Priority 2: Foundation analysis
                    PromptType.RISK_ASSESSMENT,       # Priority 3: Risk check
                    PromptType.ENTRY_TIMING,          # Priority 4: Entry decisions
                    PromptType.OPPORTUNITY_SCANNER,   # Priority 5: Opportunity identification
                    PromptType.POSITION_MANAGEMENT,   # Priority 6: Manage existing positions
                    PromptType.PROFIT_OPTIMIZATION,   # Priority 7: Optimize existing positions
                    PromptType.STRATEGY_ADAPTATION,   # Priority 8: Adapt strategy
                ]
                logger.info("🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis")

            # Filter prompts that should be executed
            executable_prompts = [
                prompt_type for prompt_type in prompt_sequence
                if self._should_execute_prompt(prompt_type, trading_context)
            ]

            if not executable_prompts:
                logger.warning("⚠️ No prompts to execute")
                return cycle_results

            # 🚀 PARALLEL EXECUTION: Use ThreadPoolExecutor for concurrent LLM calls
            with concurrent.futures.ThreadPoolExecutor(max_workers=min(len(executable_prompts), 8)) as executor:
                # Submit all prompts simultaneously
                future_to_prompt = {
                    executor.submit(self._execute_single_prompt, prompt_type, trading_context): prompt_type
                    for prompt_type in executable_prompts
                }

                logger.info(f"🚀 Submitted {len(future_to_prompt)} prompts for parallel execution")

                # Collect results as they complete
                for future in concurrent.futures.as_completed(future_to_prompt, timeout=60):
                    prompt_type = future_to_prompt[future]
                    try:
                        result = future.result()
                        cycle_results[prompt_type] = result
                        logger.info(f"✅ Completed prompt: {prompt_type.value}")

                        # Emergency exit: If emergency action taken, cancel remaining prompts
                        if (prompt_type == PromptType.EMERGENCY_RESPONSE and
                            result.success and result.response.get('ACTION') != 'MONITOR'):
                            logger.warning("🚨 Emergency action taken - cancelling remaining prompts")
                            # Cancel remaining futures
                            for remaining_future in future_to_prompt:
                                if not remaining_future.done():
                                    remaining_future.cancel()
                            break

                    except Exception as e:
                        logger.error(f"❌ Error executing prompt {prompt_type.value}: {e}")
                        # Create failed result
                        cycle_results[prompt_type] = PromptResult(
                            prompt_type=prompt_type,
                            timestamp=datetime.now(),
                            response={},
                            confidence=0.0,
                            execution_time=0.0,
                            success=False,
                            error_message=str(e)
                        )

            # Update execution times
            for prompt_type in cycle_results.keys():
                self.last_execution_times[prompt_type] = start_time

            # Log cycle completion with performance metrics
            cycle_time = time.time() - start_time
            logger.info(f"🚀 PARALLEL LLM cycle completed in {cycle_time:.2f}s - {len(cycle_results)} prompts executed concurrently")

            return cycle_results

        except Exception as e:
            logger.error(f"❌ Error in parallel LLM prompt cycle: {e}")
            return cycle_results

    def get_aggregated_decision(self, cycle_results: Dict[str, Any]) -> Any:
        """🗳️ Get unified trading decision from LLM cycle results using vote aggregator"""
        try:
            if not cycle_results:
                logger.warning("⚠️ No cycle results to aggregate")
                return self.vote_aggregator._create_default_decision("No cycle results")

            # Use vote aggregator to resolve conflicts and get unified decision
            aggregated_decision = self.vote_aggregator.aggregate_votes(cycle_results)

            logger.info(f"🗳️ Aggregated Decision: {aggregated_decision.decision.value} "
                       f"({aggregated_decision.confidence:.1f}%) - {aggregated_decision.reasoning}")

            # Log vote breakdown for debugging
            breakdown_str = ", ".join([f"{k}: {v:.1f}" for k, v in aggregated_decision.vote_breakdown.items() if v > 0])
            logger.info(f"🗳️ Vote Breakdown: {breakdown_str}")

            if aggregated_decision.conflicting_signals:
                logger.warning(f"⚠️ Conflicting signals detected - consensus: {aggregated_decision.consensus_strength:.1%}")

            return aggregated_decision

        except Exception as e:
            logger.error(f"❌ Error getting aggregated decision: {e}")
            return self.vote_aggregator._create_default_decision(f"Aggregation error: {e}")

    def execute_prompt_cycle(self, trading_context, mode: str = "full") -> Dict[str, PromptResult]:
        """🚀 OPTIMIZED: Execute LLM prompts with selective execution for performance"""

        cycle_results = {}
        start_time = time.time()

        try:

            # Update emergency flags
            self.update_emergency_flags(trading_context)

            # 🚀 PERFORMANCE OPTIMIZATION: Selective prompt execution based on mode
            if mode == "scalping":
                # Fast execution for scalping (3 essential prompts only)
                prompt_sequence = [
                    PromptType.RISK_ASSESSMENT,       # Priority 1: Risk check
                    PromptType.ENTRY_TIMING,          # Priority 2: Entry decisions
                    PromptType.OPPORTUNITY_SCANNER,   # Priority 3: Opportunity identification
                ]
                logger.info("🏃 SCALPING MODE: Executing 3 essential prompts for speed")
                delay_between_prompts = 0.05  # 50ms for scalping
            else:
                # Full analysis mode (all prompts)
                prompt_sequence = [
                    PromptType.EMERGENCY_RESPONSE,    # Priority 1: IMMEDIATE (skip others if action taken)
                    PromptType.MARKET_REGIME,         # Priority 2: Foundation analysis (informs other prompts)
                    PromptType.RISK_ASSESSMENT,       # Priority 3: Risk check before entry decisions
                    PromptType.ENTRY_TIMING,          # Priority 4: Entry decisions (depends on risk)
                    PromptType.OPPORTUNITY_SCANNER,   # Priority 5: Opportunity identification
                    PromptType.POSITION_MANAGEMENT,   # Priority 6: Manage existing positions
                    PromptType.PROFIT_OPTIMIZATION,   # Priority 7: Optimize existing positions
                    PromptType.STRATEGY_ADAPTATION,   # Priority 8: Adapt strategy based on results
                ]
                logger.info("🔍 FULL MODE: Executing all 8 prompts for comprehensive analysis")
                delay_between_prompts = 0.1  # 100ms for full mode

            # Track thread usage for performance monitoring
            thread_usage_start = getattr(self.main_window, 'thread_pool', None)
            if thread_usage_start:
                initial_threads = thread_usage_start.activeThreadCount()
                logger.info(f"📊 LLM Thread Usage Start: {initial_threads}/{thread_usage_start.maxThreadCount()} threads")

            # Execute prompts sequentially with context updates
            for i, prompt_type in enumerate(prompt_sequence):
                if self._should_execute_prompt(prompt_type, trading_context):
                    # Log progress for monitoring
                    logger.info(f"🧠 Executing prompt {i+1}/{len(prompt_sequence)}: {prompt_type.value}")

                    # Execute single prompt
                    result = self._execute_single_prompt(prompt_type, trading_context)
                    cycle_results[prompt_type] = result

                    # 🚀 PERFORMANCE FIX: Update context with previous results for next prompt
                    trading_context = self._update_context_with_result(trading_context, result)

                    # Emergency exit: If emergency action taken, skip remaining prompts
                    if (prompt_type == PromptType.EMERGENCY_RESPONSE and
                        result.success and result.response.get('ACTION') != 'MONITOR'):
                        logger.warning("🚨 Emergency action taken - skipping remaining prompts")
                        break

                    # 🚀 PERFORMANCE FIX: Yield control to prevent UI blocking
                    # Variable delay based on execution mode
                    time.sleep(delay_between_prompts)

            # Update execution times
            for prompt_type in cycle_results.keys():
                self.last_execution_times[prompt_type] = start_time

            # Log cycle completion with performance metrics
            cycle_time = time.time() - start_time
            if thread_usage_start:
                final_threads = thread_usage_start.activeThreadCount()
                logger.info(f"📊 LLM Thread Usage End: {final_threads}/{thread_usage_start.maxThreadCount()} threads")

            logger.info(f"🧠 LLM prompt cycle completed in {cycle_time:.2f}s - {len(cycle_results)} prompts executed sequentially")

            return cycle_results

        except Exception as e:
            logger.error(f"❌ Error in LLM prompt cycle: {e}")
            return cycle_results
    
    def _should_execute_prompt(self, prompt_type: PromptType, trading_context) -> bool:
        """🚀 OPTIMIZED: Enhanced prompt execution logic with context awareness"""
        try:
            # Handle both TradingContext objects and dictionaries for compatibility
            if isinstance(trading_context, dict):
                emergency_flags = trading_context.get('emergency_flags', [])
                open_positions = trading_context.get('open_positions', [])
            else:
                emergency_flags = getattr(trading_context, 'emergency_flags', [])
                open_positions = getattr(trading_context, 'open_positions', [])

            # Always execute emergency response if emergency flags exist
            if prompt_type == PromptType.EMERGENCY_RESPONSE:
                return len(emergency_flags) > 0 or self.emergency_mode

            # Position-dependent prompts
            if prompt_type in [PromptType.POSITION_MANAGEMENT, PromptType.PROFIT_OPTIMIZATION]:
                return len(open_positions) > 0

            # Entry timing only if we have position capacity
            if prompt_type == PromptType.ENTRY_TIMING:
                return self.has_position_capacity() and not self.emergency_mode

            # Check execution intervals to prevent over-execution
            last_execution = self.last_execution_times.get(prompt_type, 0)
            interval = self.prompt_intervals.get(prompt_type, 60)
            time_since_last = time.time() - last_execution

            if time_since_last < interval:
                logger.debug(f"Skipping {prompt_type.value} - executed {time_since_last:.1f}s ago (interval: {interval}s)")
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking prompt execution for {prompt_type}: {e}")
            return False

    def _execute_single_prompt(self, prompt_type: PromptType, trading_context: TradingContext) -> PromptResult:
        """🚀 OPTIMIZED: Execute a single prompt with error handling and performance tracking"""
        start_time = time.time()

        try:
            # Route to appropriate execution method
            if prompt_type == PromptType.EMERGENCY_RESPONSE:
                return self.execute_emergency_response(trading_context)
            elif prompt_type == PromptType.POSITION_MANAGEMENT:
                return self.execute_position_management(trading_context)
            elif prompt_type == PromptType.PROFIT_OPTIMIZATION:
                return self.execute_profit_optimization(trading_context)
            elif prompt_type == PromptType.MARKET_REGIME:
                return self.execute_market_regime_detection(trading_context)
            elif prompt_type == PromptType.RISK_ASSESSMENT:
                return self.execute_risk_assessment(trading_context)
            elif prompt_type == PromptType.ENTRY_TIMING:
                return self.execute_entry_timing(trading_context)
            elif prompt_type == PromptType.STRATEGY_ADAPTATION:
                return self.execute_strategy_adaptation(trading_context)
            elif prompt_type == PromptType.OPPORTUNITY_SCANNER:
                return self.execute_opportunity_scanner(trading_context)
            else:
                raise ValueError(f"Unknown prompt type: {prompt_type}")

        except Exception as e:
            logger.error(f"Error executing {prompt_type.value}: {e}")
            return PromptResult(
                prompt_type=prompt_type,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def _update_context_with_result(self, trading_context, result: PromptResult):
        """🚀 OPTIMIZED: Update trading context with previous prompt results for better decision making"""
        try:
            # Handle both TradingContext objects and dictionaries
            if isinstance(trading_context, dict):
                # If it's already a dictionary, work with it directly
                symbol = trading_context.get('symbol', 'BTC/USDT:USDT')
                current_price = trading_context.get('current_price', 0.0)
                account_balance = trading_context.get('account_balance', 1000.0)
                open_positions = trading_context.get('open_positions', [])
                market_data = trading_context.get('market_data', {})
                performance_metrics = trading_context.get('performance_metrics', {})
                emergency_flags = trading_context.get('emergency_flags', [])
            else:
                # If it's a TradingContext object, extract attributes
                symbol = getattr(trading_context, 'symbol', 'BTC/USDT:USDT')
                current_price = getattr(trading_context, 'current_price', 0.0)
                account_balance = getattr(trading_context, 'account_balance', 1000.0)
                open_positions = getattr(trading_context, 'open_positions', [])
                market_data = getattr(trading_context, 'market_data', {})
                performance_metrics = getattr(trading_context, 'performance_metrics', {})
                emergency_flags = getattr(trading_context, 'emergency_flags', [])

            # Create updated context with new information
            updated_context = TradingContext(
                symbol=symbol,
                current_price=current_price,
                account_balance=account_balance,
                open_positions=open_positions,
                market_data=market_data.copy(),  # Make a copy to avoid mutation
                performance_metrics=performance_metrics,
                emergency_flags=emergency_flags.copy(),  # Make a copy to avoid mutation
                timestamp=datetime.now()
            )

            # Update market regime if available
            if result.prompt_type == PromptType.MARKET_REGIME and result.success:
                self.market_regime = result.response.get('REGIME', 'UNKNOWN')
                # Update market data with regime information
                updated_context.market_data['regime'] = self.market_regime
                updated_context.market_data['scalp_suitability'] = result.response.get('SCALP_SUITABILITY', 'MEDIUM')

            # Update emergency flags if emergency response executed
            if result.prompt_type == PromptType.EMERGENCY_RESPONSE and result.success:
                action = result.response.get('ACTION', 'MONITOR')
                if action != 'MONITOR':
                    # Clear emergency flags if action was taken
                    updated_context.emergency_flags = []
                    self.emergency_mode = False

            # Update strategy state if strategy adaptation executed
            if result.prompt_type == PromptType.STRATEGY_ADAPTATION and result.success:
                self.strategy_state = result.response.get('STRATEGY_MODE', 'NORMAL')

            return updated_context

        except Exception as e:
            logger.error(f"Error updating context with result: {e}")
            return trading_context  # Return original context on error

    def has_active_positions(self) -> bool:
        """Check if there are active positions"""
        try:
            if hasattr(self.trading_interface, 'real_trading') and self.trading_interface.real_trading:
                positions = self.trading_interface.real_trading.get_open_positions()
                return len(positions) > 0
        except:
            pass
        return False
    
    def has_position_capacity(self) -> bool:
        """Check if we have capacity for new positions"""
        try:
            if hasattr(self.trading_interface, 'real_trading') and self.trading_interface.real_trading:
                positions = self.trading_interface.real_trading.get_open_positions()
                return len(positions) < 3  # Max 3 concurrent positions
        except:
            pass
        return True
    
    def update_emergency_flags(self, trading_context):
        """Update emergency flags based on current conditions"""
        self.emergency_flags.clear()
        
        try:
            # Check for flash crash (>2% price drop in 1 minute)
            if self.detect_flash_crash(trading_context):
                self.emergency_flags.append("FLASH_CRASH")
            
            # Check for liquidity crisis (spread >1%)
            if self.detect_liquidity_crisis(trading_context):
                self.emergency_flags.append("LIQUIDITY_CRISIS")
            
            # Check for margin risk (account risk >80%)
            if self.detect_margin_risk(trading_context):
                self.emergency_flags.append("MARGIN_RISK")
            
            # Check for unusual volume spike
            if self.detect_volume_spike(trading_context):
                self.emergency_flags.append("VOLUME_SPIKE")
            
            # Update emergency mode
            self.emergency_mode = len(self.emergency_flags) > 0
            
        except Exception as e:
            logger.error(f"Error updating emergency flags: {e}")
    
    def detect_flash_crash(self, context: TradingContext) -> bool:
        """Detect flash crash conditions"""
        # Implementation would check recent price movements
        return False
    
    def detect_liquidity_crisis(self, context: TradingContext) -> bool:
        """Detect liquidity crisis conditions"""
        # Implementation would check bid-ask spreads
        return False
    
    def detect_margin_risk(self, context: TradingContext) -> bool:
        """Detect margin risk conditions"""
        # Implementation would check account risk levels
        return False
    
    def detect_volume_spike(self, context: TradingContext) -> bool:
        """Detect unusual volume spikes"""
        # Implementation would check volume patterns
        return False

    def execute_emergency_response(self, context) -> PromptResult:
        """Execute emergency response prompt"""
        start_time = time.time()

        try:
            prompt = self.build_emergency_response_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_emergency_response(response_text)

            execution_time = time.time() - start_time

            # Execute emergency actions if needed
            if response.get('ACTION') in ['CLOSE_ALL_POSITIONS', 'CLOSE_LOSING']:
                self.execute_emergency_actions(response, context)

            return PromptResult(
                prompt_type=PromptType.EMERGENCY_RESPONSE,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in emergency response: {e}")
            return PromptResult(
                prompt_type=PromptType.EMERGENCY_RESPONSE,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_position_management(self, context) -> PromptResult:
        """Execute position management prompt"""
        start_time = time.time()

        try:
            prompt = self.build_position_management_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_position_management_response(response_text)

            execution_time = time.time() - start_time

            # Execute position management actions
            if response.get('ACTION') != 'HOLD':
                self.execute_position_actions(response, context)

            return PromptResult(
                prompt_type=PromptType.POSITION_MANAGEMENT,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in position management: {e}")
            return PromptResult(
                prompt_type=PromptType.POSITION_MANAGEMENT,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_profit_optimization(self, context) -> PromptResult:
        """Execute profit optimization prompt"""
        start_time = time.time()

        try:
            prompt = self.build_profit_optimization_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_profit_optimization_response(response_text)

            execution_time = time.time() - start_time

            # Execute profit optimization actions
            if response.get('ACTION') in ['PARTIAL_CLOSE', 'FULL_CLOSE']:
                self.execute_profit_actions(response, context)

            return PromptResult(
                prompt_type=PromptType.PROFIT_OPTIMIZATION,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in profit optimization: {e}")
            return PromptResult(
                prompt_type=PromptType.PROFIT_OPTIMIZATION,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_market_regime_detection(self, context) -> PromptResult:
        """Execute market regime detection prompt"""
        start_time = time.time()

        try:
            prompt = self.build_market_regime_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_market_regime_response(response_text)

            execution_time = time.time() - start_time

            return PromptResult(
                prompt_type=PromptType.MARKET_REGIME,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in market regime detection: {e}")
            return PromptResult(
                prompt_type=PromptType.MARKET_REGIME,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_risk_assessment(self, context) -> PromptResult:
        """Execute risk assessment prompt"""
        start_time = time.time()

        try:
            prompt = self.build_risk_assessment_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_risk_assessment_response(response_text)

            execution_time = time.time() - start_time

            return PromptResult(
                prompt_type=PromptType.RISK_ASSESSMENT,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in risk assessment: {e}")
            return PromptResult(
                prompt_type=PromptType.RISK_ASSESSMENT,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_entry_timing(self, context) -> PromptResult:
        """Execute entry timing optimization prompt"""
        start_time = time.time()

        try:
            prompt = self.build_entry_timing_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_entry_timing_response(response_text)

            execution_time = time.time() - start_time

            # 🚨 CRITICAL FIX: Don't execute actions immediately - wait for vote aggregation
            # Actions will be executed after vote aggregation determines the final decision
            # if response.get('ACTION') == 'ENTER_NOW':
            #     self.execute_entry_actions(response, context)

            return PromptResult(
                prompt_type=PromptType.ENTRY_TIMING,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in entry timing: {e}")
            return PromptResult(
                prompt_type=PromptType.ENTRY_TIMING,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_strategy_adaptation(self, context) -> PromptResult:
        """Execute strategy adaptation prompt"""
        start_time = time.time()

        try:
            prompt = self.build_strategy_adaptation_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_strategy_adaptation_response(response_text)

            execution_time = time.time() - start_time

            # Apply strategy adaptations
            self.apply_strategy_adaptations(response, context)

            return PromptResult(
                prompt_type=PromptType.STRATEGY_ADAPTATION,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in strategy adaptation: {e}")
            return PromptResult(
                prompt_type=PromptType.STRATEGY_ADAPTATION,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    def execute_opportunity_scanner(self, context) -> PromptResult:
        """Execute opportunity scanner prompt"""
        start_time = time.time()

        try:
            prompt = self.build_opportunity_scanner_prompt(context)
            response_text = self.lmstudio_runner.run_inference(prompt)
            response = self.parse_opportunity_scanner_response(response_text)

            execution_time = time.time() - start_time

            return PromptResult(
                prompt_type=PromptType.OPPORTUNITY_SCANNER,
                timestamp=datetime.now(),
                response=response,
                confidence=response.get('CONFIDENCE', 0),
                execution_time=execution_time,
                success=True
            )

        except Exception as e:
            logger.error(f"Error in opportunity scanner: {e}")
            return PromptResult(
                prompt_type=PromptType.OPPORTUNITY_SCANNER,
                timestamp=datetime.now(),
                response={},
                confidence=0,
                execution_time=time.time() - start_time,
                success=False,
                error_message=str(e)
            )

    # Import the builders and parsers
    def __init__(self, lmstudio_runner, main_window=None):
        from core.llm_prompt_builders import LLMPromptBuilders
        from core.llm_response_parsers import LLMResponseParsers
        from core.llm_action_executors import LLMActionExecutors

        self.lmstudio_runner = lmstudio_runner
        self.main_window = main_window  # Main window has all trading methods
        self.trading_interface = main_window  # For backward compatibility

        # Initialize components
        self.prompt_builders = LLMPromptBuilders(main_window)
        self.response_parsers = LLMResponseParsers()
        self.action_executors = LLMActionExecutors(main_window, main_window)
        self.vote_aggregator = UnifiedVoteAggregator()
        self.key_levels_calculator = DynamicKeyLevelsCalculator()  # 🚨 CRITICAL: Dynamic key levels  # 🗳️ NEW: Unified voting system

        # Prompt execution tracking
        self.prompt_results = {}
        self.last_execution_times = {}
        self.prompt_queue = []

        # Emergency and state management
        self.emergency_mode = False
        self.emergency_flags = []
        self.market_regime = "UNKNOWN"
        self.strategy_state = "NORMAL"

        # Performance tracking
        self.prompt_performance = {}
        self.execution_stats = {}

        # Configuration
        self.prompt_intervals = {
            PromptType.EMERGENCY_RESPONSE: 5,      # 5 seconds
            PromptType.POSITION_MANAGEMENT: 10,    # 10 seconds
            PromptType.PROFIT_OPTIMIZATION: 15,    # 15 seconds
            PromptType.MARKET_REGIME: 30,          # 30 seconds
            PromptType.RISK_ASSESSMENT: 45,        # 45 seconds
            PromptType.ENTRY_TIMING: 20,           # 20 seconds
            PromptType.STRATEGY_ADAPTATION: 120,   # 2 minutes
            PromptType.OPPORTUNITY_SCANNER: 60,    # 1 minute
        }

        # Initialize cache manager and other settings
        self._initialize_cache_and_settings()

    async def analyze_market(self, symbol: str, market_data: Dict) -> Dict[str, Any]:
        """
        Analyze market conditions for a specific symbol using LLM

        Args:
            symbol: Trading symbol to analyze
            market_data: Market data including OHLCV, orderbook, etc.

        Returns:
            Dict containing market analysis results
        """
        try:
            # 🚨 CRITICAL FIX: Create trading context with validated data
            validated_price = self.validate_price_data(market_data.get('price', 0.0), symbol)
            validated_balance = self.validate_balance_data(getattr(self.main_window, 'account_balance', 1000.0))
            validated_market_data = self.validate_market_data_quality(market_data, symbol, validated_price)

            trading_context = TradingContext(
                symbol=symbol,
                current_price=validated_price,
                account_balance=validated_balance,
                open_positions=[],
                market_data=validated_market_data,
                performance_metrics={},
                emergency_flags=self.emergency_flags,
                timestamp=datetime.now(),
                recent_prices=[(datetime.now(), validated_price)],
                recent_signals=[{'decision': 'WAIT', 'timestamp': datetime.now()}]
            )

            # Execute market regime analysis prompt
            results = self.execute_prompt_cycle(trading_context, mode="market_analysis")

            # Extract market analysis from results
            market_analysis = {}
            if PromptType.MARKET_REGIME in results:
                regime_result = results[PromptType.MARKET_REGIME]
                market_analysis['regime'] = regime_result.response.get('regime', 'UNKNOWN')
                market_analysis['confidence'] = regime_result.confidence
                market_analysis['reasoning'] = regime_result.response.get('reasoning', '')

            if PromptType.OPPORTUNITY_SCANNER in results:
                opportunity_result = results[PromptType.OPPORTUNITY_SCANNER]
                market_analysis['opportunities'] = opportunity_result.response.get('opportunities', [])
                market_analysis['risk_level'] = opportunity_result.response.get('risk_level', 'MEDIUM')

            # Add basic technical analysis if no LLM results
            if not market_analysis:
                market_analysis = {
                    'regime': 'UNKNOWN',
                    'confidence': 0.5,
                    'reasoning': 'Basic analysis - LLM not available',
                    'opportunities': [],
                    'risk_level': 'MEDIUM'
                }

            return market_analysis

        except Exception as e:
            logger.error(f"Error in market analysis for {symbol}: {e}")
            return {
                'regime': 'ERROR',
                'confidence': 0.0,
                'reasoning': f'Analysis failed: {str(e)}',
                'opportunities': [],
                'risk_level': 'HIGH'
            }

    def _initialize_cache_and_settings(self):
        """Initialize cache manager and other settings"""
        try:
            from utils.cache_manager import CacheManager
            self.cache = CacheManager()
        except ImportError:
            logger.warning("CacheManager not available, using basic caching")
            self.cache = None
        self.w_risk, self.w_timing, self.w_opp = 1.0, 1.0, 1.0
        self.current_support, self.current_resistance = None, None

        logger.info("LLM Prompt Orchestrator initialized")

    def set_main_window(self, main_window):
        """Set main window reference for action executors (already set in __init__)"""
        # Main window is already set in __init__, but keep this method for compatibility
        self.main_window = main_window
        self.action_executors.main_window = main_window

    # Add prompt building methods
    def build_emergency_response_prompt(self, context):
        return self.prompt_builders.build_emergency_response_prompt(context)

    def build_position_management_prompt(self, context):
        return self.prompt_builders.build_position_management_prompt(context)

    def build_profit_optimization_prompt(self, context):
        return self.prompt_builders.build_profit_optimization_prompt(context)

    def build_market_regime_prompt(self, context):
        return self.prompt_builders.build_market_regime_prompt(context)

    def build_risk_assessment_prompt(self, context):
        return self.prompt_builders.build_risk_assessment_prompt(context)

    def build_entry_timing_prompt(self, context):
        return self.prompt_builders.build_entry_timing_prompt(context)

    def build_strategy_adaptation_prompt(self, context):
        return self.prompt_builders.build_strategy_adaptation_prompt(context)

    def build_opportunity_scanner_prompt(self, context):
        return self.prompt_builders.build_opportunity_scanner_prompt(context)

    # Add response parsing methods
    def parse_emergency_response(self, response_text):
        return self.response_parsers.parse_emergency_response(response_text)

    def parse_position_management_response(self, response_text):
        return self.response_parsers.parse_position_management_response(response_text)

    def parse_profit_optimization_response(self, response_text):
        return self.response_parsers.parse_profit_optimization_response(response_text)

    def parse_market_regime_response(self, response_text):
        return self.response_parsers.parse_market_regime_response(response_text)

    def parse_risk_assessment_response(self, response_text):
        return self.response_parsers.parse_risk_assessment_response(response_text)

    def parse_entry_timing_response(self, response_text):
        return self.response_parsers.parse_entry_timing_response(response_text)

    def parse_strategy_adaptation_response(self, response_text):
        return self.response_parsers.parse_strategy_adaptation_response(response_text)

    def parse_opportunity_scanner_response(self, response_text):
        return self.response_parsers.parse_opportunity_scanner_response(response_text)

    # Add action execution methods
    def execute_emergency_actions(self, response, context):
        return self.action_executors.execute_emergency_actions(response, context)

    def execute_position_actions(self, response, context):
        return self.action_executors.execute_position_actions(response, context)

    def execute_profit_actions(self, response, context):
        return self.action_executors.execute_profit_actions(response, context)

    def execute_entry_actions(self, response, context):
        return self.action_executors.execute_entry_actions(response, context)

    def apply_strategy_adaptations(self, response, context):
        return self.action_executors.apply_strategy_adaptations(response, context)

    def run_cycle(self):
        """Test harness: run a single orchestrator cycle as described in the Copilot instructions."""
        # 1) Fetch price & cache it
        price = self.market_api.get_current_price(getattr(self, 'symbol', 'TEST'))
        if self.cache:
            self.cache.add_price(price)
        # 2) Compute & store support/resistance (use test values or defaults)
        support = getattr(self, 'current_support', price * 0.99)
        resistance = getattr(self, 'current_resistance', price * 1.01)
        self.current_support, self.current_resistance = support, resistance
        # 3) Pre-filter
        if not price_in_action_zone(price, support, resistance):
            if self.cache:
                self.cache.add_signal('WAIT', 0.0)
                signal_history_len = len(self.cache.signal_history) if self.cache.signal_history else 0
                return self.schedule_auto_refresh(120 if signal_history_len < 3 else 300)
            else:
                return self.schedule_auto_refresh(120)
        # 4) Build & call prompts with context
        ctx = self.cache.get_context() if self.cache else {}
        r = self.llm_client.call('risk')
        t = self.llm_client.call('timing')
        o = self.llm_client.call('opp')
        # 5) Aggregate, record, execute
        decision, scores = aggregate_signal([r, t, o], [self.w_risk, self.w_timing, self.w_opp])
        confidence = scores[decision]/(self.w_risk+self.w_timing+self.w_opp)
        if self.cache:
            self.cache.add_signal(decision, confidence)
        # 6) Schedule next run using proper scheduling logic
        if self.cache and hasattr(self.cache, 'price_history'):
            ph = [p for _, p in self.cache.price_history]
            if len(ph) > 0 and price_in_action_zone(ph[-1], self.current_support, self.current_resistance):
                return self.schedule_auto_refresh(15)
        return self.schedule_auto_refresh(60)

class LmStudioRunner:
    """
    LLM runner for auto-trade integration using LM Studio's OpenAI-compatible API.
    """
    def __init__(self, api_url="http://localhost:1234/v1/chat/completions", model="phi-3.1-mini-128k-instruct"):
        self.api_url = api_url
        self.model = model

    def call(self, prompt: str) -> str:
        headers = {"Content-Type": "application/json"}
        data = {
            "model": self.model,
            "messages": [
                {"role": "system", "content": "You are a trading assistant. Only output the decision, confidence, and rationale."},
                {"role": "user", "content": prompt}
            ],
            "max_tokens": 256,
            "temperature": 0.2,
            "stream": False
        }
        try:
            response = requests.post(self.api_url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            result = response.json()
            # Extract the assistant's reply
            return result['choices'][0]['message']['content']
        except Exception as e:
            return f"DECISION: WAIT\nCONFIDENCE: 0.0\nRATIONALE: LLM API error: {e}"

    def validate_price_data(self, price, symbol):
        """🚨 CRITICAL FIX: Validate price data quality"""
        try:
            if price is None or price <= 0:
                # Symbol-specific fallbacks
                symbol_defaults = {
                    'BTC/USDT:USDT': 95000.0,
                    'ETH/USDT:USDT': 3500.0,
                    'DOGE/USDT:USDT': 0.19,
                    'SOL/USDT:USDT': 200.0,
                    'ADA/USDT:USDT': 0.45,
                    'XRP/USDT:USDT': 0.60,
                    'MATIC/USDT:USDT': 0.85,
                    'DOT/USDT:USDT': 7.50
                }
                fallback_price = symbol_defaults.get(symbol, 0.17)
                logger.warning(f"Invalid price {price} for {symbol}, using fallback: {fallback_price}")
                return fallback_price

            return float(price)

        except Exception as e:
            logger.error(f"Error validating price data: {e}")
            return 0.17

    def validate_balance_data(self, balance):
        """🚨 CRITICAL FIX: Validate account balance data"""
        try:
            if balance is None or balance <= 0:
                logger.warning(f"Invalid balance {balance}, using fallback: 104.74")
                return 104.74

            return float(balance)

        except Exception as e:
            logger.error(f"Error validating balance data: {e}")
            return 104.74

    def validate_market_data_quality(self, market_data, symbol, current_price):
        """🚨 CRITICAL FIX: Validate market data quality and calculate dynamic metrics"""
        try:
            validated_data = market_data.copy()

            # Validate bid/ask prices and calculate spread
            bid = market_data.get('bid', 0)
            ask = market_data.get('ask', 0)

            if bid <= 0 or ask <= 0:
                validated_data['bid'] = current_price * 0.9995
                validated_data['ask'] = current_price * 1.0005
                spread_pct = 0.1
                logger.warning(f"Fixed invalid bid/ask for {symbol}: {bid}/{ask}")
            else:
                # Calculate real spread percentage
                spread_pct = ((ask - bid) / current_price) * 100
                validated_data['bid'] = bid
                validated_data['ask'] = ask

            validated_data['spread_pct'] = spread_pct

            # Calculate dynamic ATR (Average True Range)
            atr_value = self._calculate_dynamic_atr(market_data, current_price, symbol)
            validated_data['atr'] = atr_value

            # Calculate volume ratio
            volume_ratio = self._calculate_volume_ratio(market_data, symbol)
            validated_data['volume_ratio'] = volume_ratio

            # Calculate price range percentage
            price_range_pct = self._calculate_price_range_pct(market_data, current_price)
            validated_data['price_range_pct'] = price_range_pct

            # Calculate and populate signal data
            signals = self._calculate_signal_data(market_data, current_price, symbol)
            validated_data['signals'] = signals

            # Calculate and populate regime data
            regime_data = self._calculate_regime_data(market_data, current_price, symbol)
            validated_data['regime'] = regime_data

            # 🚨 CRITICAL: Calculate dynamic key levels
            try:
                # Create market data DataFrame for key levels calculation
                if hasattr(market_data, 'get') and 'recent_prices' in market_data:
                    recent_prices = market_data.get('recent_prices', [])
                    if len(recent_prices) >= 20:
                        # Create DataFrame from recent prices
                        df_data = {
                            'close': recent_prices,
                            'high': [p * 1.001 for p in recent_prices],  # Estimate high
                            'low': [p * 0.999 for p in recent_prices],   # Estimate low
                            'volume': [1000] * len(recent_prices)        # Default volume
                        }
                        market_df = pd.DataFrame(df_data)

                        # Calculate dynamic key levels
                        key_levels_result = self.key_levels_calculator.calculate_key_levels(
                            market_df, current_price, symbol, lookback_periods=min(50, len(recent_prices))
                        )

                        # Update market data with dynamic key levels
                        validated_data['key_levels'] = {
                            'nearest_support': key_levels_result.nearest_support.price,
                            'nearest_resistance': key_levels_result.nearest_resistance.price,
                            'support_strength': key_levels_result.support_strength,
                            'resistance_strength': key_levels_result.resistance_strength,
                            'price_position': key_levels_result.price_position,
                            'breakout_probability': key_levels_result.breakout_probability,
                            'recommended_action': key_levels_result.recommended_action,
                            'confluence_zones': key_levels_result.confluence_zones,
                            'level_density': key_levels_result.level_density
                        }

                        # Update legacy fields for backward compatibility
                        validated_data['support_level'] = key_levels_result.nearest_support.price
                        validated_data['resistance_level'] = key_levels_result.nearest_resistance.price
                        validated_data['nearest_support'] = key_levels_result.nearest_support.price
                        validated_data['nearest_resistance'] = key_levels_result.nearest_resistance.price

                        logger.info(f"✅ Dynamic key levels calculated for {symbol}")
                        logger.info(f"Support: {key_levels_result.nearest_support.price:.6f} (strength: {key_levels_result.support_strength:.1f})")
                        logger.info(f"Resistance: {key_levels_result.nearest_resistance.price:.6f} (strength: {key_levels_result.resistance_strength:.1f})")
                        logger.info(f"Position: {key_levels_result.price_position}, Action: {key_levels_result.recommended_action}")

                    else:
                        # Fallback to simple calculation
                        self._apply_fallback_key_levels(validated_data, current_price, symbol)
                else:
                    # Fallback to simple calculation
                    self._apply_fallback_key_levels(validated_data, current_price, symbol)

            except Exception as e:
                logger.error(f"Error calculating dynamic key levels: {e}")
                # Fallback to simple calculation
                self._apply_fallback_key_levels(validated_data, current_price, symbol)

            # Add data quality indicator
            validated_data['data_quality'] = 'VALIDATED'
            validated_data['validation_timestamp'] = datetime.now().isoformat()

            logger.debug(f"Market metrics for {symbol}: ATR={atr_value:.6f}, Spread={spread_pct:.3f}%, Vol={volume_ratio:.1f}x, Range={price_range_pct:.2f}%")

            return validated_data

        except Exception as e:
            logger.error(f"Error validating market data: {e}")
            return market_data

    def _calculate_dynamic_atr(self, market_data, current_price, symbol):
        """Calculate dynamic Average True Range"""
        try:
            # Try to get recent price data
            recent_prices = market_data.get('recent_prices', [])
            if len(recent_prices) >= 2:
                # Calculate ATR from recent price movements
                price_changes = []
                for i in range(1, min(len(recent_prices), 20)):  # Last 20 periods
                    high = max(recent_prices[i-1], recent_prices[i])
                    low = min(recent_prices[i-1], recent_prices[i])
                    price_changes.append(abs(high - low))

                if price_changes:
                    atr = sum(price_changes) / len(price_changes)
                    return atr

            # Fallback: estimate ATR from spread and volatility
            spread_pct = market_data.get('spread_pct', 0.1)
            volatility_factor = market_data.get('volatility', 2.0)
            estimated_atr = current_price * (spread_pct / 100) * volatility_factor
            return max(estimated_atr, current_price * 0.0001)  # Minimum 0.01% of price

        except Exception as e:
            logger.warning(f"Error calculating ATR for {symbol}: {e}")
            return current_price * 0.001  # 0.1% of current price as fallback

    def _calculate_volume_ratio(self, market_data, symbol):
        """🚨 ENHANCED: Calculate volume ratio compared to average"""
        try:
            current_volume = market_data.get('volume', 0)
            avg_volume = market_data.get('avg_volume', 0)

            if avg_volume > 0 and current_volume > 0:
                ratio = current_volume / avg_volume
                logger.debug(f"Volume ratio for {symbol}: {ratio:.2f} (current: {current_volume}, avg: {avg_volume})")
                return ratio

            # Fallback 1: use 24h volume data if available
            volume_24h = market_data.get('volume_24h', 0)
            if volume_24h > 0:
                # Estimate current volume as fraction of 24h volume
                estimated_current = volume_24h / (24 * 60)  # Per minute estimate
                estimated_avg = estimated_current * 0.8  # Assume current is 20% above average
                ratio = max(1.0, estimated_current / estimated_avg)
                logger.debug(f"Volume ratio for {symbol} (24h fallback): {ratio:.2f}")
                return ratio

            # Fallback 2: Check for any volume indicators
            quote_volume = market_data.get('quote_volume', 0)
            base_volume = market_data.get('base_volume', 0)

            if quote_volume > 0 or base_volume > 0:
                # If we have any volume data, assume normal volume
                logger.debug(f"Volume ratio for {symbol} (volume present): 1.1")
                return 1.1  # Slightly above neutral to indicate confirmed volume

            # Final fallback: neutral volume ratio
            logger.debug(f"Volume ratio for {symbol} (no data): 1.0")
            return 1.0  # Neutral volume ratio

        except Exception as e:
            logger.warning(f"Error calculating volume ratio for {symbol}: {e}")
            return 1.0

    def _calculate_price_range_pct(self, market_data, current_price):
        """Calculate price range percentage"""
        try:
            # Try to get high/low from market data
            high_24h = market_data.get('high_24h', 0)
            low_24h = market_data.get('low_24h', 0)

            if high_24h > 0 and low_24h > 0:
                range_pct = ((high_24h - low_24h) / current_price) * 100
                return max(range_pct, 0.1)  # Minimum 0.1%

            # Fallback: estimate from recent price movements
            recent_prices = market_data.get('recent_prices', [])
            if len(recent_prices) >= 10:
                recent_high = max(recent_prices[-10:])
                recent_low = min(recent_prices[-10:])
                range_pct = ((recent_high - recent_low) / current_price) * 100
                return max(range_pct, 0.1)

            # Final fallback: estimate from ATR
            atr = market_data.get('atr', current_price * 0.001)
            range_pct = (atr / current_price) * 100 * 2  # ATR * 2 as range estimate
            return max(range_pct, 1.0)  # Minimum 1%

        except Exception as e:
            logger.warning(f"Error calculating price range: {e}")
            return 1.0

    def _calculate_signal_data(self, market_data, current_price, symbol):
        """Calculate ML ensemble and technical signal data"""
        try:
            signals = {}

            # Calculate ML Ensemble signal
            ensemble_decision, ensemble_confidence = self._calculate_ml_ensemble(market_data, current_price)
            signals['ensemble_decision'] = ensemble_decision
            signals['confidence'] = ensemble_confidence

            # Calculate Technical signal
            technical_signal, momentum_score = self._calculate_technical_signal(market_data, current_price)
            signals['technical_signal'] = technical_signal
            signals['momentum_score'] = momentum_score

            # Calculate Volume confirmation
            volume_confirmation = self._calculate_volume_confirmation(market_data)
            signals['volume_confirmation'] = volume_confirmation

            # Calculate Orderflow bias
            orderflow_bias = self._calculate_orderflow_bias(market_data)
            signals['orderflow_bias'] = orderflow_bias

            logger.debug(f"Calculated signals for {symbol}: Ensemble={ensemble_decision}({ensemble_confidence:.1f}%), Technical={technical_signal}({momentum_score:.2f})")

            return signals

        except Exception as e:
            logger.warning(f"Error calculating signal data for {symbol}: {e}")
            return {
                'ensemble_decision': 'NEUTRAL',
                'confidence': 50.0,
                'technical_signal': 'NEUTRAL',
                'momentum_score': 0.0,
                'volume_confirmation': 'PENDING',
                'orderflow_bias': 'NEUTRAL'
            }

    def _calculate_ml_ensemble(self, market_data, current_price):
        """Calculate ML ensemble decision and confidence"""
        try:
            # Get recent price data for ML analysis
            recent_prices = market_data.get('recent_prices', [])
            volume_ratio = market_data.get('volume_ratio', 1.0)
            atr = market_data.get('atr', current_price * 0.001)

            # Simple ML-like ensemble logic
            signals = []

            # Price momentum signal
            if len(recent_prices) >= 5:
                recent_change = (recent_prices[-1] - recent_prices[-5]) / recent_prices[-5]
                if recent_change > 0.002:  # 0.2% up
                    signals.append(('BULLISH', 0.7))
                elif recent_change < -0.002:  # 0.2% down
                    signals.append(('BEARISH', 0.7))
                else:
                    signals.append(('NEUTRAL', 0.5))

            # Volume signal
            if volume_ratio > 1.5:
                signals.append(('BULLISH', 0.6))
            elif volume_ratio < 0.7:
                signals.append(('BEARISH', 0.6))
            else:
                signals.append(('NEUTRAL', 0.5))

            # Volatility signal
            atr_pct = (atr / current_price) * 100
            if atr_pct > 2.0:  # High volatility
                signals.append(('NEUTRAL', 0.4))  # Uncertain in high vol
            else:
                signals.append(('NEUTRAL', 0.6))  # More confident in low vol

            # Aggregate signals
            if not signals:
                return 'NEUTRAL', 50.0

            signal_counts = {'BULLISH': 0, 'BEARISH': 0, 'NEUTRAL': 0}
            total_confidence = 0

            for signal, confidence in signals:
                signal_counts[signal] += confidence
                total_confidence += confidence

            # Find dominant signal
            dominant_signal = max(signal_counts.items(), key=lambda x: x[1])
            ensemble_decision = dominant_signal[0]
            ensemble_confidence = (dominant_signal[1] / total_confidence) * 100

            return ensemble_decision, min(95.0, max(30.0, ensemble_confidence))

        except Exception as e:
            logger.warning(f"Error in ML ensemble calculation: {e}")
            return 'NEUTRAL', 50.0

    def _calculate_technical_signal(self, market_data, current_price):
        """Calculate technical analysis signal and momentum"""
        try:
            recent_prices = market_data.get('recent_prices', [])

            if len(recent_prices) < 10:
                return 'NEUTRAL', 0.0

            # Use current_price as fallback if recent_prices is empty
            if not recent_prices:
                recent_prices = [current_price] * 10

            # Calculate momentum (rate of change)
            short_ma = sum(recent_prices[-5:]) / 5  # 5-period MA
            long_ma = sum(recent_prices[-10:]) / 10  # 10-period MA

            momentum_score = ((short_ma - long_ma) / long_ma) * 100

            # Determine technical signal
            if momentum_score > 0.5:
                technical_signal = 'BULLISH'
            elif momentum_score < -0.5:
                technical_signal = 'BEARISH'
            else:
                technical_signal = 'NEUTRAL'

            return technical_signal, momentum_score

        except Exception as e:
            logger.warning(f"Error in technical signal calculation: {e}")
            return 'NEUTRAL', 0.0

    def _calculate_volume_confirmation(self, market_data):
        """🚨 CRITICAL FIX: Calculate volume confirmation status - ULTRA PERMISSIVE"""
        try:
            volume_ratio = market_data.get('volume_ratio', 1.0)

            # 🚨 ULTRA PERMISSIVE: Almost always confirm volume unless extremely low
            if volume_ratio >= 1.1:
                return 'CONFIRMED'  # High volume
            elif volume_ratio < 0.5:
                return 'WEAK'       # Only extremely low volume is weak
            else:
                return 'CONFIRMED'  # Everything else (0.5-1.1) is confirmed

            # 🚨 REMOVED: No more PENDING status - only CONFIRMED or WEAK

        except Exception as e:
            logger.warning(f"Error in volume confirmation calculation: {e}")
            return 'CONFIRMED'  # 🚨 FIXED: Default to CONFIRMED instead of PENDING

    def _apply_fallback_key_levels(self, validated_data: Dict[str, Any], current_price: float, symbol: str):
        """🚨 CRITICAL: Apply fallback key levels when dynamic calculation fails"""
        try:
            # Simple percentage-based levels
            fallback_support = current_price * 0.995
            fallback_resistance = current_price * 1.005

            validated_data['key_levels'] = {
                'nearest_support': fallback_support,
                'nearest_resistance': fallback_resistance,
                'support_strength': 25.0,  # Weak strength for fallback
                'resistance_strength': 25.0,
                'price_position': 'BETWEEN_LEVELS',
                'breakout_probability': 50.0,
                'recommended_action': 'NEUTRAL',
                'confluence_zones': [],
                'level_density': 1.0
            }

            # Update legacy fields
            validated_data['support_level'] = fallback_support
            validated_data['resistance_level'] = fallback_resistance
            validated_data['nearest_support'] = fallback_support
            validated_data['nearest_resistance'] = fallback_resistance

            logger.warning(f"Applied fallback key levels for {symbol}: Support {fallback_support:.6f}, Resistance {fallback_resistance:.6f}")

        except Exception as e:
            logger.error(f"Error applying fallback key levels: {e}")

    def _calculate_orderflow_bias(self, market_data):
        """Calculate orderflow bias"""
        try:
            bid = market_data.get('bid', 0)
            ask = market_data.get('ask', 0)

            if bid > 0 and ask > 0:
                # Simple orderflow bias based on bid/ask pressure
                mid_price = (bid + ask) / 2
                current_price = market_data.get('price', mid_price)

                if current_price > mid_price * 1.0001:
                    return 'BULLISH'
                elif current_price < mid_price * 0.9999:
                    return 'BEARISH'
                else:
                    return 'NEUTRAL'

            return 'NEUTRAL'

        except Exception as e:
            logger.warning(f"Error in orderflow bias calculation: {e}")
            return 'NEUTRAL'

    def _calculate_regime_data(self, market_data, current_price, symbol):
        """Calculate market regime classification and risk adjustments"""
        try:
            # Get market metrics
            atr = market_data.get('atr', current_price * 0.001)
            volume_ratio = market_data.get('volume_ratio', 1.0)
            recent_prices = market_data.get('recent_prices', [])

            # Calculate volatility level
            atr_pct = (atr / current_price) * 100

            # Calculate trend strength
            trend_strength = 0.0
            trend_direction = 'NEUTRAL'

            if len(recent_prices) >= 10:
                # Calculate trend using linear regression slope
                x_values = list(range(len(recent_prices)))
                y_values = recent_prices

                n = len(recent_prices)
                sum_x = sum(x_values)
                sum_y = sum(y_values)
                sum_xy = sum(x * y for x, y in zip(x_values, y_values))
                sum_x2 = sum(x * x for x in x_values)

                # Linear regression slope
                slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
                trend_strength = abs(slope / current_price) * 100  # Normalize to percentage

                if slope > current_price * 0.001:  # 0.1% threshold
                    trend_direction = 'BULLISH'
                elif slope < -current_price * 0.001:
                    trend_direction = 'BEARISH'

            # Determine primary regime
            regime_type = self._classify_market_regime(atr_pct, volume_ratio, trend_strength, trend_direction)

            # Calculate regime-based risk adjustments
            risk_adjustments = self._calculate_regime_risk_adjustments(regime_type, atr_pct, volume_ratio)

            regime_data = {
                'regime_type': regime_type,
                'trend_direction': trend_direction,
                'trend_strength': trend_strength,
                'volatility_level': self._classify_volatility(atr_pct),
                'volume_level': self._classify_volume(volume_ratio),
                'scalp_suitability': self._calculate_scalp_suitability(regime_type, atr_pct, volume_ratio),
                'risk_adjustments': risk_adjustments
            }

            logger.debug(f"Regime analysis for {symbol}: {regime_type} | Trend: {trend_direction}({trend_strength:.2f}%) | Vol: {atr_pct:.3f}%")

            return regime_data

        except Exception as e:
            logger.warning(f"Error calculating regime data for {symbol}: {e}")
            return {
                'regime_type': 'RANGING_TIGHT',
                'trend_direction': 'NEUTRAL',
                'trend_strength': 0.0,
                'volatility_level': 'MEDIUM',
                'volume_level': 'NORMAL',
                'scalp_suitability': 'MEDIUM',
                'risk_adjustments': {'leverage_multiplier': 0.6, 'position_size_multiplier': 0.8}
            }

    def _classify_market_regime(self, atr_pct, volume_ratio, trend_strength, trend_direction):
        """Classify market regime based on volatility, volume, and trend"""
        try:
            # High volatility regimes
            if atr_pct > 3.0:
                if volume_ratio > 2.0:
                    return 'NEWS_DRIVEN'  # High vol + high volume = news event
                elif trend_strength > 2.0:
                    if trend_direction == 'BULLISH':
                        return 'TRENDING_BULL'
                    elif trend_direction == 'BEARISH':
                        return 'TRENDING_BEAR'
                    else:
                        return 'RANGING_VOLATILE'
                else:
                    return 'RANGING_VOLATILE'  # High vol, no clear trend

            # Medium to low volatility regimes
            elif atr_pct > 1.5:
                if trend_strength > 1.5:
                    if trend_direction == 'BULLISH':
                        return 'TRENDING_BULL'
                    elif trend_direction == 'BEARISH':
                        return 'TRENDING_BEAR'
                    else:
                        return 'BREAKOUT_PENDING'
                else:
                    return 'RANGING_VOLATILE'

            # Low volatility regimes
            else:
                if trend_strength > 1.0:
                    if trend_direction == 'BULLISH':
                        return 'TRENDING_BULL'
                    elif trend_direction == 'BEARISH':
                        return 'TRENDING_BEAR'
                    else:
                        return 'BREAKOUT_PENDING'
                else:
                    return 'RANGING_TIGHT'  # Low vol, low trend = tight range

        except Exception as e:
            logger.warning(f"Error classifying market regime: {e}")
            return 'RANGING_TIGHT'

    def _classify_volatility(self, atr_pct):
        """Classify volatility level"""
        if atr_pct > 3.0:
            return 'HIGH'
        elif atr_pct > 1.5:
            return 'MEDIUM'
        else:
            return 'LOW'

    def _classify_volume(self, volume_ratio):
        """Classify volume level"""
        if volume_ratio > 1.5:
            return 'HIGH'
        elif volume_ratio > 0.8:
            return 'NORMAL'
        else:
            return 'LOW'

    def _calculate_scalp_suitability(self, regime_type, atr_pct, volume_ratio):
        """Calculate scalping suitability based on regime"""
        try:
            # Trending markets with good volume are best for scalping
            if regime_type in ['TRENDING_BULL', 'TRENDING_BEAR']:
                if volume_ratio > 1.2 and 1.0 < atr_pct < 3.0:
                    return 'HIGH'
                elif volume_ratio > 0.8:
                    return 'MEDIUM'
                else:
                    return 'LOW'

            # Ranging markets can be good if volatility is right
            elif regime_type == 'RANGING_VOLATILE':
                if 1.5 < atr_pct < 2.5 and volume_ratio > 1.0:
                    return 'MEDIUM'
                else:
                    return 'LOW'

            # Tight ranges are generally poor for scalping
            elif regime_type == 'RANGING_TIGHT':
                return 'LOW'

            # News-driven can be good but risky
            elif regime_type == 'NEWS_DRIVEN':
                return 'MEDIUM'

            # Breakout pending can be good if you catch the breakout
            elif regime_type == 'BREAKOUT_PENDING':
                return 'MEDIUM'

            else:
                return 'MEDIUM'

        except Exception as e:
            logger.warning(f"Error calculating scalp suitability: {e}")
            return 'MEDIUM'

    def _calculate_regime_risk_adjustments(self, regime_type, atr_pct, volume_ratio):
        """Calculate risk adjustments based on regime"""
        try:
            # Base adjustments by regime type
            regime_adjustments = {
                'TRENDING_BULL': {'leverage_multiplier': 1.0, 'position_size_multiplier': 1.0},
                'TRENDING_BEAR': {'leverage_multiplier': 1.0, 'position_size_multiplier': 1.0},
                'RANGING_TIGHT': {'leverage_multiplier': 0.6, 'position_size_multiplier': 0.8},
                'RANGING_VOLATILE': {'leverage_multiplier': 0.5, 'position_size_multiplier': 0.7},
                'BREAKOUT_PENDING': {'leverage_multiplier': 0.8, 'position_size_multiplier': 0.9},
                'NEWS_DRIVEN': {'leverage_multiplier': 0.3, 'position_size_multiplier': 0.5}
            }

            base_adjustments = regime_adjustments.get(regime_type, {'leverage_multiplier': 0.6, 'position_size_multiplier': 0.8})

            # Additional volatility adjustments
            if atr_pct > 4.0:  # Very high volatility
                base_adjustments['leverage_multiplier'] *= 0.5
                base_adjustments['position_size_multiplier'] *= 0.7
            elif atr_pct > 2.5:  # High volatility
                base_adjustments['leverage_multiplier'] *= 0.7
                base_adjustments['position_size_multiplier'] *= 0.8

            # Volume adjustments
            if volume_ratio < 0.5:  # Very low volume
                base_adjustments['leverage_multiplier'] *= 0.8
                base_adjustments['position_size_multiplier'] *= 0.9

            # Ensure minimums
            base_adjustments['leverage_multiplier'] = max(0.2, base_adjustments['leverage_multiplier'])
            base_adjustments['position_size_multiplier'] = max(0.3, base_adjustments['position_size_multiplier'])

            return base_adjustments

        except Exception as e:
            logger.warning(f"Error calculating regime risk adjustments: {e}")
            return {'leverage_multiplier': 0.6, 'position_size_multiplier': 0.8}

__all__ = [
    'LLMPromptOrchestrator',
    'aggregate_signal',
    'price_in_action_zone',
    'PromptType',
    'PromptResult',
    'TradingContext',
    'LmStudioRunner',
]
