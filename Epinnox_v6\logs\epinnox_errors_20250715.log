2025-07-15 06:55:21,585 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 06:55:21,586 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for BTC/USDT:USDT
2025-07-15 06:55:51,270 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 06:55:51,271 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for BTC/USDT:USDT
2025-07-15 06:56:20,005 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 06:56:52,152 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 06:57:20,143 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 06:57:21,306 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 06:57:21,307 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 06:57:51,187 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 06:57:52,532 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 06:57:52,532 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 06:58:21,109 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 06:58:21,889 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 06:58:22,029 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 06:58:51,479 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 06:58:52,253 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 06:58:52,253 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 06:59:22,830 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 06:59:23,619 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 06:59:23,619 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 06:59:49,612 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 06:59:50,398 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 06:59:50,399 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:00:21,757 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:00:23,041 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:00:23,042 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:00:49,960 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:00:50,736 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:00:50,736 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:01:23,151 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:01:23,910 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:01:23,910 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:01:50,232 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:01:51,013 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:01:51,014 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:02:23,536 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:02:24,323 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:02:24,323 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:03:20,771 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:03:22,071 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:03:22,071 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:03:37,276 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:03:38,081 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:03:38,084 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:04:06,653 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:04:07,470 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:04:07,472 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:04:35,295 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:04:36,075 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:04:36,077 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:05:06,824 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:05:38,270 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:06:06,666 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:06:07,471 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:06:07,473 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:06:37,497 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:06:38,292 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:06:38,293 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:07:13,273 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:07:14,063 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:07:14,065 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:07:35,143 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:07:35,916 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:07:35,917 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:08:12,653 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:08:13,430 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:08:13,431 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:08:35,312 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:08:36,092 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:08:36,094 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:09:05,867 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:09:06,646 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:09:06,647 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:09:35,378 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:09:36,178 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:09:36,181 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:10:05,344 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:10:06,170 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:10:06,172 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:10:39,770 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:10:40,580 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:10:40,582 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:11:05,380 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:11:06,183 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:11:06,186 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:11:35,340 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:11:36,134 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:11:36,138 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:12:05,282 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:12:06,074 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:12:06,076 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:12:39,511 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:12:40,294 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:12:40,294 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:13:06,499 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:13:07,281 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:13:07,281 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:13:37,464 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:13:38,217 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:13:38,217 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:14:09,203 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:14:09,994 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:14:09,995 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:14:35,103 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:14:35,875 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:14:35,875 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:15:14,146 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:15:15,355 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:15:15,360 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:15:35,124 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:15:35,877 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:15:35,877 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:16:05,450 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:16:06,220 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:16:06,220 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:16:35,743 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:16:36,510 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:16:36,510 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:17:06,499 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:17:07,274 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:17:07,276 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:17:36,935 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:17:37,709 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:17:37,710 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:18:10,007 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:18:10,786 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:18:10,786 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:18:35,305 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:18:36,574 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:18:36,580 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:19:08,396 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:19:09,164 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:19:09,166 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:19:39,506 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:19:40,326 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:19:40,326 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:20:05,282 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:20:06,051 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:20:06,055 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:20:37,199 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:20:37,960 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:20:37,960 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:21:05,253 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:21:06,023 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:21:06,027 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:21:37,376 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:21:38,134 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:21:38,136 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:22:05,571 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:22:06,347 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:22:06,347 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:22:35,271 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:22:36,502 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:22:36,502 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:23:06,667 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:23:07,486 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:23:07,487 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:23:36,067 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:23:37,161 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:23:37,162 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:24:05,431 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:24:06,765 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:24:06,765 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:24:36,796 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:24:37,583 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:24:37,583 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:25:08,536 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:25:09,295 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:25:09,296 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:25:35,120 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:25:35,889 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:25:35,889 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:26:06,521 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:26:07,316 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:26:07,318 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:26:36,705 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:26:37,832 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:26:37,832 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:27:10,720 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:27:11,514 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:27:11,514 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:27:35,334 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:27:36,613 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:27:36,614 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:28:07,347 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:28:08,200 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:28:08,202 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:28:35,286 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:28:36,068 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:28:36,068 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:29:11,030 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:29:11,821 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:29:11,821 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:29:35,200 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:29:35,989 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:29:35,990 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:30:07,707 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:30:08,509 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:30:08,520 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:30:35,703 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:30:36,510 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:30:36,511 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:31:05,844 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:31:36,782 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:32:05,209 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:32:39,090 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:32:39,867 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:32:39,867 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:33:09,054 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:33:09,838 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:33:09,838 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:33:35,214 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:33:36,000 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:33:36,000 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:34:15,766 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:34:16,973 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:34:16,974 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:34:35,231 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:34:36,033 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:34:36,033 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:35:05,133 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:35:05,909 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:35:05,909 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:35:35,348 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:35:36,130 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:35:36,130 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:36:05,565 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:36:06,343 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:36:06,343 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:36:35,354 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:36:36,150 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:36:36,150 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:37:08,732 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:37:09,511 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:37:09,512 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:37:35,321 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:37:36,101 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:37:36,101 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:38:08,467 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:38:09,238 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:38:09,238 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:38:35,308 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:38:36,072 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:38:36,072 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:39:06,734 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:39:07,516 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:39:07,516 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:39:39,433 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:39:40,219 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:39:40,219 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:40:05,231 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:40:06,026 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:40:06,026 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:40:35,178 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:40:35,952 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:40:35,952 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:41:08,658 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:41:09,529 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:41:09,529 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:41:35,268 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:41:36,042 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:41:36,043 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:42:09,103 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:42:09,917 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:42:09,919 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:42:35,678 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:42:36,463 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:42:36,466 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:43:08,264 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:43:09,028 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:43:09,031 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:43:35,485 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:43:36,262 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:43:36,263 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:44:06,472 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:44:07,241 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:44:07,241 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:44:36,690 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:44:37,454 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:44:37,454 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:45:11,466 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:45:12,235 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:45:12,235 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:45:35,273 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:45:36,049 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:45:36,049 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:46:06,488 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:46:07,764 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:46:07,764 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:46:38,381 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:46:39,156 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:46:39,172 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:47:06,339 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:47:07,299 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:47:07,315 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:47:35,638 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:47:36,433 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:47:36,436 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:48:10,060 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:48:10,837 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:48:10,837 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:48:35,230 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:48:36,011 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:48:36,014 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:49:07,610 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:49:08,853 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:49:08,854 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:49:41,924 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:49:42,727 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:49:42,727 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:50:07,302 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:50:08,083 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:50:08,085 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:50:35,750 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:50:36,541 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:50:36,544 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:51:05,967 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:51:06,766 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:51:06,767 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:51:35,530 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:52:11,002 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:52:11,808 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:52:11,808 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:52:35,237 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:52:36,044 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:52:36,044 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:53:08,583 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:53:09,381 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:53:09,381 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:53:35,627 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:53:36,391 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:53:36,391 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:54:05,237 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:54:06,022 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:54:06,022 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:54:35,107 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:54:35,874 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:54:35,875 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:55:09,177 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:55:09,966 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:55:09,966 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:55:35,214 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:55:35,983 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:55:35,985 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:56:09,125 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:56:10,387 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:56:10,387 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:56:37,167 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:56:37,970 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:56:37,970 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:57:05,291 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:57:06,098 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:57:06,098 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:57:40,451 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:57:41,319 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:57:41,321 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 07:58:09,106 - core.llm_action_executors - ERROR - Error checking position capacity: 'PositionTracker' object has no attribute 'get_open_positions'
2025-07-15 07:58:09,948 - trading.intelligent_limit_order_manager - ERROR - Failed to place exchange order for DOGE/USDT:USDT
2025-07-15 07:58:09,948 - core.llm_action_executors - ERROR - ❌ Failed to place intelligent limit order for DOGE/USDT:USDT
2025-07-15 09:57:09,791 - websocket - ERROR - error from callback <bound method WebSocketClient._on_message of <data.websocket_client.WebSocketClient object at 0x00000131CC2744C0>>: wrapped C/C++ object of type WebSocketClient has been deleted
2025-07-15 09:57:09,793 - websocket - ERROR - error from callback <bound method WebSocketClient._on_error of <data.websocket_client.WebSocketClient object at 0x00000131CC2744C0>>: wrapped C/C++ object of type WebSocketClient has been deleted
