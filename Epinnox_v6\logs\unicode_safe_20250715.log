2025-07-15 09:35:10 - INFO - core.timer_coordinator - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-15 09:35:10 - INFO - core.emergency_stop_coordinator - Emergency Stop Coordinator initialized
2025-07-15 09:35:10 - DEBUG - asyncio - Using proactor: IocpProactor
2025-07-15 09:35:10 - INFO - core.autonomous_trading_orchestrator - Autonomous Trading Orchestrator initialized in live mode
2025-07-15 09:35:10 - INFO - core.autonomous_trading_orchestrator - [LAUNCH] Initializing Autonomous Trading Orchestrator...
2025-07-15 09:35:10 - INFO - core.autonomous_trading_orchestrator - Initializing autonomous trading system...
2025-07-15 09:35:10 - INFO - core.error_recovery_system - [ERROR_RECOVERY] Error Recovery System initialized
2025-07-15 09:35:10 - INFO - core.error_recovery_system - [ERROR_RECOVERY] Health monitoring started
2025-07-15 09:35:10 - INFO - core.error_recovery_system - [ERROR_RECOVERY] Error Recovery System ready
2025-07-15 09:35:10 - INFO - core.autonomous_trading_orchestrator - [OK] Error recovery system initialized
2025-07-15 09:35:11 - INFO - core.websocket_manager - WebSocket Manager initialized for htx
2025-07-15 09:35:11 - INFO - core.websocket_manager - WebSocket Manager initialized for wss://api.huobi.pro/ws
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added message handler: default
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added connection handler
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added connection handler
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added disconnection handler
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added error handler
2025-07-15 09:35:11 - INFO - core.websocket_manager - WebSocket Manager initialized for wss://api.hbdm.com/swap-ws
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added message handler: default
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added connection handler
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added connection handler
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added disconnection handler
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added error handler
2025-07-15 09:35:11 - INFO - core.websocket_manager - WebSocket Manager initialized for wss://api-aws.huobi.pro/ws
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added message handler: default
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added connection handler
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added connection handler
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added disconnection handler
2025-07-15 09:35:11 - INFO - core.websocket_manager - Added error handler
2025-07-15 09:35:11 - INFO - data.market_data_manager - Initialized 3 WebSocket endpoints
2025-07-15 09:35:11 - INFO - data.market_data_manager - Market data manager initialized for htx
2025-07-15 09:35:11 - INFO - data.market_data_manager - Starting market data manager...
2025-07-15 09:35:11 - INFO - data.market_data_manager - Started WebSocket connection: market
2025-07-15 09:35:11 - INFO - data.market_data_manager - Started WebSocket connection: futures
2025-07-15 09:35:11 - INFO - data.market_data_manager - Started WebSocket connection: backup
2025-07-15 09:35:11 - INFO - core.websocket_manager - Starting WebSocket listener for wss://api.huobi.pro/ws
2025-07-15 09:35:11 - INFO - core.websocket_manager - Starting WebSocket listener for wss://api.hbdm.com/swap-ws
2025-07-15 09:35:11 - INFO - core.websocket_manager - Starting WebSocket listener for wss://api-aws.huobi.pro/ws
2025-07-15 09:35:13 - ERROR - data.market_data_manager - Failed to establish any WebSocket connections
2025-07-15 09:35:13 - ERROR - core.autonomous_trading_orchestrator - Failed to initialize data manager: No WebSocket connections available
2025-07-15 09:35:13 - ERROR - core.autonomous_trading_orchestrator - Failed to initialize autonomous trading system: No WebSocket connections available
2025-07-15 09:35:13 - ERROR - core.autonomous_trading_orchestrator - [ERROR] Orchestrator initialization failed
2025-07-15 09:36:57 - INFO - core.timer_coordinator - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-15 09:36:57 - INFO - core.emergency_stop_coordinator - Emergency Stop Coordinator initialized
2025-07-15 09:36:57 - INFO - core.unified_execution_engine - Unified Execution Engine initialized with LIMIT orders enforcement
2025-07-15 09:36:57 - INFO - core.dynamic_risk_manager - Dynamic Risk Manager initialized
2025-07-15 09:38:57 - INFO - core.timer_coordinator - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-15 09:38:57 - INFO - core.emergency_stop_coordinator - Emergency Stop Coordinator initialized
2025-07-15 09:38:57 - INFO - core.unified_execution_engine - Unified Execution Engine initialized with LIMIT orders enforcement
2025-07-15 09:38:57 - INFO - core.dynamic_risk_manager - Dynamic Risk Manager initialized
2025-07-15 09:39:08 - WARNING - core.llm_action_executors - [WARNING] Intelligent Limit Order Manager not available
2025-07-15 09:39:08 - INFO - core.llm_action_executors - [ROCKET] LLM Action Executors initialized with intelligent limit order system
2025-07-15 09:39:08 - INFO - utils.cache_manager - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-15 09:39:08 - INFO - utils.cache_manager - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-15 09:39:08 - INFO - core.llm_orchestrator - LLM Prompt Orchestrator initialized
