2025-07-16 20:44:39,839 - main - INFO - Epinnox v6 starting up...
2025-07-16 20:44:39,907 - core.performance_monitor - INFO - Performance monitoring started
2025-07-16 20:44:39,907 - core.performance_monitor - INFO - Performance monitor initialized with 10s interval
2025-07-16 20:44:39,907 - main - INFO - Performance monitoring initialized
2025-07-16 20:44:39,928 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-16 20:44:39,939 - utils.cache_manager - INFO - Starting automatic cache cleanup
2025-07-16 20:44:39,941 - utils.cache_manager - INFO - Auto cleanup complete: 0 files removed, 0.00 MB freed
2025-07-16 20:44:44,396 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Initialized unified timer coordinator
2025-07-16 20:44:48,697 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-16 20:44:48,698 - core.emergency_stop_coordinator - INFO - Emergency Stop Coordinator initialized
2025-07-16 20:44:48,698 - core.emergency_stop_coordinator - INFO - [OK] Registered module for emergency stop: main_window
2025-07-16 20:44:50,538 - config.autonomous_config - INFO - Configuration loaded from configs/autonomous_trading.yaml
2025-07-16 20:44:52,045 - data.live_data_manager - INFO - Successfully subscribed to live data for BTC/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-16 20:44:53,316 - websocket - INFO - Websocket connected
2025-07-16 20:44:55,634 - trading.position_tracker - INFO - Position Tracker initialized
2025-07-16 20:44:56,052 - llama.runner - INFO - Loaded OpenAI API key from models_config.yaml
2025-07-16 20:44:56,052 - llama.runner - INFO - Using model path: C:\Users\<USER>\.lmstudio\models\lmstudio-community\Phi-3.1-mini-128k-instruct-GGUF\Phi-3.1-mini-128k-instruct-Q4_K_M.gguf
2025-07-16 20:44:56,053 - llama.runner - INFO - Using guard path: C:\Users\<USER>\Documents\dev\Epinnox_v6\models\Llama-Guard-3-8B.gguf
2025-07-16 20:44:56,053 - llama.runner - INFO - Using binary path: C:\Users\<USER>\Documents\dev\llama.cpp\main
2025-07-16 20:44:56,072 - llama.lmstudio_runner - INFO - Loaded LMStudio configuration from models_config.yaml
2025-07-16 20:44:58,296 - llama.lmstudio_runner - INFO - Discovered 8 models: ['phi-3.1-mini-128k-instruct', 'openthinker3-7b', 'mistral-7b-claude-chat', 'fine_tuned_qwen1.7b', 'tsfc-model-0.0.1', 'codestral-22b-v0.1', 'deepseek-r1-distill-qwen-7b', 'text-embedding-nomic-embed-text-v1.5']
2025-07-16 20:44:58,296 - llama.lmstudio_runner - INFO - Selected preferred model from config: phi-3.1-mini-128k-instruct
2025-07-16 20:44:58,297 - llama.lmstudio_runner - INFO - Initialized LMStudio with model: phi-3.1-mini-128k-instruct
2025-07-16 20:44:58,300 - trading.intelligent_limit_order_manager - INFO - Intelligent Limit Order Manager initialized for professional scalping
2025-07-16 20:44:58,300 - core.llm_action_executors - INFO - ✅ Intelligent Limit Order Manager initialized
2025-07-16 20:44:58,301 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-16 20:44:58,301 - utils.cache_manager - INFO - Cache manager initialized: cache, max_age=24h, max_size=100MB
2025-07-16 20:44:58,301 - core.llm_orchestrator - INFO - LLM Prompt Orchestrator initialized
2025-07-16 20:44:58,306 - core.signal_hierarchy - INFO - Signal Hierarchy initialized
2025-07-16 20:44:58,392 - storage.database_manager - INFO - Database tables initialized successfully with performance optimizations
2025-07-16 20:44:58,393 - storage.database_manager - INFO - Database manager initialized: data\epinnox_trading.db
2025-07-16 20:44:58,393 - storage.session_manager - INFO - Session Manager initialized
2025-07-16 20:44:58,400 - storage.database_manager - INFO - Created session: live_BTCUSDTUSDT_20250716_204458_6e6dff43
2025-07-16 20:44:58,400 - storage.session_manager - INFO - Started session: live_BTCUSDTUSDT_20250716_204458_6e6dff43
2025-07-16 20:44:58,662 - core.risk_management_system - INFO - 🛡️ Risk Management System initialized
2025-07-16 20:44:58,678 - core.error_handling_system - INFO - 🛡️ Error Handling System initialized
2025-07-16 20:44:58,678 - core.error_handling_system - INFO - 📊 Component registered: exchange_connection
2025-07-16 20:44:58,678 - core.error_handling_system - INFO - 📊 Component registered: trading_interface
2025-07-16 20:44:58,679 - core.error_handling_system - INFO - 📊 Component registered: market_data
2025-07-16 20:44:58,679 - core.error_handling_system - INFO - 📊 Component registered: llm_orchestrator
2025-07-16 20:44:58,681 - core.error_handling_system - INFO - 🔍 Health monitoring started
2025-07-16 20:44:58,699 - core.monitoring_dashboard - INFO - 📊 Monitoring Dashboard initialized
2025-07-16 20:44:58,700 - core.symbol_scanner - INFO - SymbolScanner initialized with 8 symbols
2025-07-16 20:44:58,700 - core.timer_coordinator - INFO - [TIMER_COORDINATOR] Registered timer 'symbol_scanner_update': interval=30.0s, priority=MEDIUM
2025-07-16 20:44:58,701 - core.symbol_scanner - INFO - [SYMBOL_SCANNER] Registered with timer coordinator (interval: 30.0s)
2025-07-16 20:44:58,701 - core.llm_action_executors - WARNING - ⚠️ Intelligent Limit Order Manager not available
2025-07-16 20:44:58,701 - core.llm_action_executors - INFO - 🚀 LLM Action Executors initialized with intelligent limit order system
2025-07-16 20:45:12,872 - data.live_data_manager - INFO - Successfully subscribed to live data for DOGE/USDT:USDT on timeframes: ['1m', '5m', '15m']
2025-07-16 20:45:20,559 - core.llm_orchestrator - INFO - 🏃 SCALPING MODE: Executing 3 essential prompts in PARALLEL for maximum speed
2025-07-16 20:45:20,566 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 20:45:20,566 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 20:45:20,566 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 20:45:20,567 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-16 20:45:20,567 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 20:45:20,567 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 20:45:20,568 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (60.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.211822/$0.211823
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.210825 (Distance: 0.47%)
Resistance: $0.217074
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 20:45:20,567 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 20:45:20,567 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-16 20:45:20,568 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 20:45:20,569 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-16 20:45:20,570 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: MODERATE_RISK
💰 Balance: $40.44 | Health: Moderate Risk - Reduced Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.211823 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | ...
2025-07-16 20:45:20,571 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 20:45:38,188 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 898 chars
2025-07-16 20:45:38,188 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 75%, TAKE_PROFIT: -3.0%, STOP_LOSS: -4.0%, EXPLANATION: Given the moderate risk account status and current market conditions with a neutral sector momentum but normal volatility levels, it is prudent to adopt a conservative approach by shorting DOGE/USDT due to its poten...
2025-07-16 20:45:38,188 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 960, Completion: 220, Total: 1180
2025-07-16 20:45:38,189 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-16 20:45:38,193 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 75.0, 'STOP_LOSS': -4.0, 'EXPLANATION': "GIVEN THE MODERATE RISK ACCOUNT STATUS AND CURRENT MARKET CONDITIONS WITH A NEUTRAL SECTOR MOMENTUM BUT NORMAL VOLATILITY LEVELS, IT IS PRUDENT TO ADOPT A CONSERVATIVE APPROACH BY SHORTING DOGE/USDT DUE TO ITS POTENTIAL OVERVALUATION IN COMPARISON TO HISTORICAL PRICE STABILITY (LAST 5 PRICES SHOW NO SIGNIFICANT CHANGE). THE TAKE-PROFIT AT -3.0% AND STOP LOSS AT -4.0% ARE SET WITHIN THE MODERATE RISK ACCOUNT'S HEALTH CONSTRAINTS, ENSURING THAT EXPOSURE REMAINS LIMITED WHILE ALLOWING FOR SOME PROFIT POTENTIAL IF DOGE/USDT SHOWS A DOWNWARD TREND IN ALIGNMENT WITH HISTORICAL PATTERNS (LAST 5 SIGNALS INDICATE 'WAIT', SUGGESTING CAUTION BUT NOT COMPLETE AVOIDANCE OF ACTION). THIS STRATEGY ALIGNS WELL WITH THE OPPORTUNITY CRITERIA AND ACCOUNT PRESERVATION REQUIREMENTS, PRIORITIZING SURVIVAL OVER HIGH PROFITS.", 'ACTION': 'ENTER_NOW'}
2025-07-16 20:45:38,193 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-16 20:45:38,194 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (REVERSAL)
2025-07-16 20:45:38,194 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-16 20:45:39,873 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 200 chars
2025-07-16 20:45:39,874 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signals with favorable spread and price near key levels"
}...
2025-07-16 20:45:39,874 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 853, Completion: 89, Total: 942
2025-07-16 20:45:39,875 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 20:45:39,875 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 20:45:39,875 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 20:45:39,876 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 20:45:41,347 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 252 chars
2025-07-16 20:45:41,348 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 92,
  "entry_reason": "Breakout above resistance level at $1350 with volume spike indicating strong buying interest.",
  "take_profit": 3.0,
  "stop_loss": 1.5,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-16 20:45:41,348 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 102, Total: 276
2025-07-16 20:45:41,348 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-16 20:45:41,349 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-16 20:45:41,349 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-16 20:45:41,349 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-16 20:45:41,351 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 20.79s - 3 prompts executed concurrently
2025-07-16 20:45:41,351 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%) | Final decision made based on LLM votes
2025-07-16 20:45:41,352 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%) | Final decision made based on LLM votes
2025-07-16 20:45:41,352 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, SHORT: 1.5, WAIT: 2.8
2025-07-16 20:45:41,352 - core.llm_orchestrator - WARNING - ⚠️ Conflicting signals detected - consensus: 45.9%
2025-07-16 20:45:41,353 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%) | Final decision made based on LLM votes
2025-07-16 20:45:41,354 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%) | Final decision made based on LLM votes
2025-07-16 20:45:41,354 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, SHORT: 1.5, WAIT: 2.8
2025-07-16 20:45:41,354 - core.llm_orchestrator - WARNING - ⚠️ Conflicting signals detected - consensus: 45.9%
2025-07-16 21:05:01,015 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-16 21:05:01,016 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:05:01,016 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:05:01,016 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:05:01,017 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-16 21:05:01,017 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:05:01,017 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:05:01,017 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:05:01,017 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-16 21:05:01,018 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:05:01,018 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:05:01,018 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:05:01,018 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:05:01,018 - core.llm_orchestrator - INFO - 🚀 Submitted 5 prompts for parallel execution
2025-07-16 21:05:01,019 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-16 21:05:01,019 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.211822/$0.211823
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.210731 (Distance: 0.50%)
Resistance: $0.212849
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 21:05:01,019 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:05:01,020 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.211790 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-16 21:05:01,020 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:05:01,021 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-16 21:05:01,021 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:05:01,022 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:05:07,785 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1191 chars
2025-07-16 21:05:07,786 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 80%, TAKE_PROFIT: 60%, STOP_LOSS: -30%, EXPLANATION: The DOGE/USDT pair shows a neutral sector momentum and normal volatility with an average volume profile. Given the account's healthy status, there is room for conservative growth while adhering to risk management criter...
2025-07-16 21:05:07,786 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 937, Completion: 276, Total: 1213
2025-07-16 21:05:07,786 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-16 21:05:07,787 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 80.0, 'TAKE_PROFIT': 60.0, 'STOP_LOSS': -30.0, 'EXPLANATION': "THE DOGE/USDT PAIR SHOWS A NEUTRAL SECTOR MOMENTUM AND NORMAL VOLATILITY WITH AN AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS, THERE IS ROOM FOR CONSERVATIVE GROWTH WHILE ADHERING TO RISK MANAGEMENT CRITERIA SET BY THE USER (2% PER TRADE). WITH HIGH SETUP QUALITY INDICATED IN TERMS OF CLEAR PATTERNS OR STRONG SIGNALS DESPITE NO SPECIFIC RECENT SIGNAL CHANGE, AND CONSIDERING A 1-3% VOLATILITY PREFERENCE WHICH ALIGNS WITH DOGE/USDT'S HISTORICAL RANGE. THE RECOMMENDED TAKE PROFIT IS AT +60%, ENSURING THAT THE POSITION REMAINS PROFITABLE WHILE ALLOWING FOR SOME UPSIDE POTENTIAL WITHOUT OVEREXPOSURE TO RISK BEYOND HEALTHY LIMITS SET BY ACCOUNT PRESERVATION CRITERIA (70% MAX EXPOSURE). A STOP LOSS OF -30% PROVIDES A BALANCE BETWEEN PROTECTING CAPITAL AND NOT BEING TOO CONSERVATIVE, WHICH COULD MISS OUT ON FAVORABLE PRICE MOVEMENTS. THE CONFIDENCE LEVEL IS HIGH DUE TO THE ALIGNMENT WITH OPPORTUNITY CRITERIA SUCH AS LIQUIDITY REQUIREMENTS MET THROUGH AVERAGE SPREADS IN THIS PAIR AND AN ACCEPTABLE RISK/REWARD RATIO THAT COMPENSATES FOR POTENTIAL DOWNSIDE WITHIN HEALTHY ACCOUNT PARAMETERS.", 'ACTION': 'ENTER_NOW'}
2025-07-16 21:05:07,787 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-16 21:05:07,787 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-16 21:05:07,788 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-16 21:05:09,369 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 175 chars
2025-07-16 21:05:09,369 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 80,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Momentum neutral but spread favorable."
}...
2025-07-16 21:05:09,369 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 841, Completion: 84, Total: 925
2025-07-16 21:05:09,370 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:05:09,370 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:05:09,371 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 21:05:09,371 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 21:05:10,830 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-16 21:05:10,831 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-16 21:05:10,831 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 88, Total: 684
2025-07-16 21:05:10,831 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-16 21:05:10,832 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-16 21:05:10,832 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-16 21:05:10,832 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-16 21:05:15,973 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1472 chars
2025-07-16 21:05:15,974 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "risk_adjustment": {"min": 0.5, "max": 2.0},
  "hold_time_target": "8", // in minutes as per the current strategy's average hold time
  "entry_threshold": 70, // maintaining at least a 70% confidence level for entry threshold to stay consistent with previous performance and reduce risk of false ...
2025-07-16 21:05:15,974 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 340, Total: 959
2025-07-16 21:05:15,974 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['min', 'max']
2025-07-16 21:05:15,974 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['MIN', 'MAX', 'CONFIDENCE']
2025-07-16 21:05:15,974 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-16 21:05:15,974 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-16 21:05:15,975 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-16 21:05:17,398 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 261 chars
2025-07-16 21:05:17,398 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a sudden spike in volume indicating strong buying interest.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-16 21:05:17,399 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 99, Total: 273
2025-07-16 21:05:17,399 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-16 21:05:17,399 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-16 21:05:17,400 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-16 21:05:17,400 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-16 21:05:17,402 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 16.39s - 5 prompts executed concurrently
2025-07-16 21:05:17,403 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (51.5%) - Decision based on weighted votes: LONG (51.5%) | Final decision made based on LLM votes
2025-07-16 21:05:17,403 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (51.5%) - Decision based on weighted votes: LONG (51.5%) | Final decision made based on LLM votes
2025-07-16 21:05:17,403 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.6, WAIT: 3.4
2025-07-16 21:05:22,728 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $18.97, Required Margin: $0.95, Account Balance: $34.22
2025-07-16 21:05:22,728 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-16 21:05:32,004 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-16 21:05:32,006 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:05:32,006 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:05:32,007 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:05:32,007 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:05:32,007 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-16 21:05:32,007 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-16 21:05:32,007 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.212273/$0.212274
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.211252 (Distance: 0.50%)
Resistance: $0.213376
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 21:05:32,008 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:05:32,008 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:05:35,385 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 140 chars
2025-07-16 21:05:35,386 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "regime": "RANGING_TIGHT",
  "confidence": 85,
  "scalp_suitability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "LOW"
}...
2025-07-16 21:05:35,386 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 66, Total: 662
2025-07-16 21:05:35,386 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level']
2025-07-16 21:05:35,387 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-16 21:05:35,387 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-16 21:05:35,388 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-16 21:05:36,943 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 212 chars
2025-07-16 21:05:36,943 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 78,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Neutral signals and favorable spread indicate a potential entry opportunity."
}...
2025-07-16 21:05:36,943 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 88, Total: 930
2025-07-16 21:05:36,944 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:05:36,944 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:05:36,944 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 21:05:36,944 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 21:05:36,945 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 4.94s - 2 prompts executed concurrently
2025-07-16 21:05:36,946 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (75.4%) - Decision based on weighted votes: LONG (75.4%) | Final decision made based on LLM votes
2025-07-16 21:05:36,946 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (75.4%) - Decision based on weighted votes: LONG (75.4%) | Final decision made based on LLM votes
2025-07-16 21:05:36,947 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 2.0, WAIT: 0.6
2025-07-16 21:06:01,089 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-16 21:06:01,094 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:06:01,095 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:06:01,096 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-16 21:06:01,096 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:06:01,096 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:06:01,097 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:06:01,098 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-16 21:06:01,098 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:06:01,098 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.212286/$0.212287
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.211157 (Distance: 0.50%)
Resistance: $0.213279
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 21:06:01,098 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-16 21:06:01,099 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.212218 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-16 21:06:01,100 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:06:01,099 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:06:07,324 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 977 chars
2025-07-16 21:06:07,324 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 75%, TAKE_PROFIT: 60%, STOP_LOSS: -30%, EXPLANATION: The DOGE/USDT pair shows a neutral sector momentum with normal volatility and average volume profile. Given the account's healthy status, there is room for conservative risk-taking while maintaining an acceptable profit...
2025-07-16 21:06:07,324 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 235, Total: 1173
2025-07-16 21:06:07,325 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-16 21:06:07,326 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 75.0, 'TAKE_PROFIT': 60.0, 'STOP_LOSS': -30.0, 'EXPLANATION': "THE DOGE/USDT PAIR SHOWS A NEUTRAL SECTOR MOMENTUM WITH NORMAL VOLATILITY AND AVERAGE VOLUME PROFILE. GIVEN THE ACCOUNT'S HEALTHY STATUS, THERE IS ROOM FOR CONSERVATIVE RISK-TAKING WHILE MAINTAINING AN ACCEPTABLE PROFIT MARGIN WITHIN OUR 2% PER TRADE LIMIT. A LONG POSITION ON DOGE ALIGNS WELL WITH HISTORICAL CONTEXT AS IT HAS BEEN IN A STEADY UPTREND BASED ON PAST PRICE MOVEMENTS AND SIGNALS ARE CONSISTENTLY 'WAIT', INDICATING POTENTIAL BUILDUP BEFORE THE NEXT MOVE UPWARDS, WHICH JUSTIFIES THIS DECISION DESPITE NOT HAVING AN IMMEDIATE STRONG SETUP QUALITY DUE TO RECENT MOM BEING FLAT. THE TAKE-PROFIT IS SET AT 60% OF ENTRY VALUE CONSIDERING A CONSERVATIVE APPROACH IN LINE WITH ACCOUNT HEALTH AND VOLATILITY PREFERENCE; STOP LOSS AT -30% ENSURES RISK MANAGEMENT WITHIN THE MAXIMUM EXPOSURE LIMIT, THUS PRESERVING CAPITAL WHILE SEEKING GROWTH POTENTIAL FROM THIS PAIRING FOR FUTURE PROFIT.", 'ACTION': 'ENTER_NOW'}
2025-07-16 21:06:07,326 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-16 21:06:07,326 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (MOMENTUM)
2025-07-16 21:06:07,326 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-16 21:06:08,914 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 205 chars
2025-07-16 21:06:08,914 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Price near support level with favorable spread and neutral momentum."
}...
2025-07-16 21:06:08,914 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 87, Total: 929
2025-07-16 21:06:08,915 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:06:08,915 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:06:08,915 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 21:06:08,917 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 21:06:10,558 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 319 chars
2025-07-16 21:06:10,558 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a strong candlestick pattern (e.g., hammer or engulfing). Current price is at the lower end of our target range.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3 minutes",
  "...
2025-07-16 21:06:10,558 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 118, Total: 292
2025-07-16 21:06:10,559 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-16 21:06:10,559 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-16 21:06:10,559 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-16 21:06:10,559 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-16 21:06:10,561 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 9.47s - 3 prompts executed concurrently
2025-07-16 21:06:10,562 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (54.6%) - Decision based on weighted votes: LONG (54.6%) | Final decision made based on LLM votes
2025-07-16 21:06:10,562 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (54.6%) - Decision based on weighted votes: LONG (54.6%) | Final decision made based on LLM votes
2025-07-16 21:06:10,562 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.2, WAIT: 2.7
2025-07-16 21:06:15,908 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $18.98, Required Margin: $0.95, Account Balance: $34.24
2025-07-16 21:06:15,909 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-16 21:06:31,070 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-16 21:06:31,072 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:06:31,073 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:06:31,073 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:06:31,073 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-16 21:06:31,073 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-16 21:06:31,074 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:06:31,074 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:06:31,075 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.212325/$0.212326
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.211238 (Distance: 0.50%)
Resistance: $0.213361
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 21:06:31,076 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:06:35,064 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 195 chars
2025-07-16 21:06:35,064 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signals but favorable spread and low volume spike."
}...
2025-07-16 21:06:35,065 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 840, Completion: 89, Total: 929
2025-07-16 21:06:35,065 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:06:35,065 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:06:35,066 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 21:06:35,066 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 21:06:36,869 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 237 chars
2025-07-16 21:06:36,869 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM",
  "REASONING": "Dynamic regime detection based on volatility and trend analysis"
}
```...
2025-07-16 21:06:36,870 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 110, Total: 706
2025-07-16 21:06:36,870 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-16 21:06:36,871 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-16 21:06:36,871 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-16 21:06:36,872 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-16 21:06:36,874 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.80s - 2 prompts executed concurrently
2025-07-16 21:06:36,876 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (75.7%) - Decision based on weighted votes: LONG (75.7%) | Final decision made based on LLM votes
2025-07-16 21:06:36,876 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (75.7%) - Decision based on weighted votes: LONG (75.7%) | Final decision made based on LLM votes
2025-07-16 21:06:36,877 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 0.6
2025-07-16 21:07:01,002 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-16 21:07:01,003 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:07:01,003 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:07:01,003 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-16 21:07:01,003 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:07:01,004 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:07:01,003 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-16 21:07:01,004 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:07:01,005 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.212300/$0.212301
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.211248 (Distance: 0.50%)
Resistance: $0.213372
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 21:07:01,005 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:07:04,546 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 221 chars
2025-07-16 21:07:04,546 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with strong volume indicators.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-16 21:07:04,547 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-16 21:07:04,547 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-16 21:07:04,547 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-16 21:07:04,547 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-16 21:07:04,548 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-16 21:07:06,090 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 201 chars
2025-07-16 21:07:06,090 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 78,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Price near key levels with favorable spread and neutral momentum."
}...
2025-07-16 21:07:06,091 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 841, Completion: 86, Total: 927
2025-07-16 21:07:06,091 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:07:06,092 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:07:06,092 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 21:07:06,093 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 21:07:06,095 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 5.09s - 2 prompts executed concurrently
2025-07-16 21:07:06,095 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%) | Final decision made based on LLM votes
2025-07-16 21:07:06,096 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%) | Final decision made based on LLM votes
2025-07-16 21:07:06,096 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 2.0, WAIT: 2.7
2025-07-16 21:07:10,861 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $18.15, Required Margin: $0.91, Account Balance: $32.70
2025-07-16 21:07:10,862 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-16 21:07:31,103 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-16 21:07:31,104 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:07:31,104 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:07:31,104 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:07:31,105 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-16 21:07:31,105 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:07:31,106 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:07:31,106 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:07:31,106 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:07:31,106 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.212291/$0.212292
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.211239 (Distance: 0.50%)
Resistance: $0.213363
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 21:07:31,106 - core.llm_orchestrator - INFO - 🚀 Submitted 4 prompts for parallel execution
2025-07-16 21:07:31,107 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-16 21:07:31,107 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:07:31,107 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:07:31,108 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.212301 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-16 21:07:31,109 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-16 21:07:31,109 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:07:31,111 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:07:34,902 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 155 chars
2025-07-16 21:07:34,903 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "REGIME": "RANGING_TIGHT",
  "CONFIDENCE": 75,
  "SCALP_SUITABILITY": "MEDIUM",
  "RECOMMENDED_TIMEFRAME": "1m",
  "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-16 21:07:34,903 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 88, Total: 684
2025-07-16 21:07:34,904 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-16 21:07:34,905 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-16 21:07:34,905 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-16 21:07:34,906 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-16 21:07:36,536 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 207 chars
2025-07-16 21:07:36,537 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral technical indicators with favorable spread and no volume spike"
}...
2025-07-16 21:07:36,537 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 91, Total: 933
2025-07-16 21:07:36,538 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:07:36,538 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:07:36,538 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 21:07:36,538 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 21:07:38,532 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 339 chars
2025-07-16 21:07:38,533 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "risk_adjustment": [0.5, 2.0],
  "hold_time_target": "8",
  "entry_threshold": 70,
  "exit_threshold": 60,
  "sizing_method": "FIXED_RISK",
  "reasoning": "Maintain current strategy - performing well with a moderate risk adjustment to account for recent poor performance and normal market...
2025-07-16 21:07:38,533 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 124, Total: 743
2025-07-16 21:07:38,533 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-16 21:07:38,534 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-16 21:07:38,534 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.0x
2025-07-16 21:07:38,534 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.0x, Hold time 8min
2025-07-16 21:07:38,535 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-16 21:07:41,938 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 823 chars
2025-07-16 21:07:41,938 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 60%, STOP_LOSS: -4%, EXPLANATION: The DOGE/USDT pair shows a neutral sector momentum with good liquidity and an excellent spread of less than 0.2%. Given the account's healthy status, normal trading parameters are acceptable for this conservative entry s...
2025-07-16 21:07:41,939 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 202, Total: 1140
2025-07-16 21:07:41,939 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-16 21:07:41,939 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 60.0, 'STOP_LOSS': -4.0, 'EXPLANATION': "THE DOGE/USDT PAIR SHOWS A NEUTRAL SECTOR MOMENTUM WITH GOOD LIQUIDITY AND AN EXCELLENT SPREAD OF LESS THAN 0.2%. GIVEN THE ACCOUNT'S HEALTHY STATUS, NORMAL TRADING PARAMETERS ARE ACCEPTABLE FOR THIS CONSERVATIVE ENTRY STRATEGY WITHIN A MODERATE RISK ENVIRONMENT. WITH HISTORICAL CONTEXT SHOWING NO RECENT PRICE MOVEMENT (MOM AT +0.0%), BUT STILL MAINTAINABLE SIGNALS ('WAIT'), IT SUGGESTS THAT THERE IS POTENTIAL WITHOUT IMMEDIATE HIGH-RISK EXPOSURE. THE TAKE PROFIT AND STOP LOSS PERCENTAGES ARE SET TO ENSURE THE ACCOUNT'S HEALTH REMAINS UNCOMPROMISED WHILE CAPITALIZING ON A POTENTIALLY PROFITABLE OPPORTUNITY WITH AN EXPECTED RISK/REWARD RATIO OF 1:3, WHICH ALIGNS WELL WITHIN OUR CONSERVATIVE APPROACH FOR THIS SPECIFIC MARKET CONDITION.", 'ACTION': 'ENTER_NOW'}
2025-07-16 21:07:41,940 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-16 21:07:41,940 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-16 21:07:41,940 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-16 21:07:41,941 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.84s - 4 prompts executed concurrently
2025-07-16 21:07:41,942 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (82.1%) - Decision based on weighted votes: LONG (82.1%) | Final decision made based on LLM votes
2025-07-16 21:07:41,942 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (82.1%) - Decision based on weighted votes: LONG (82.1%) | Final decision made based on LLM votes
2025-07-16 21:07:41,942 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.5, WAIT: 0.8
2025-07-16 21:08:01,883 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-16 21:08:01,884 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:08:01,885 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:08:01,886 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.013%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-16 21:08:01,886 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:08:01,886 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:08:01,887 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:08:01,887 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:08:01,887 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-16 21:08:01,887 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:08:01,889 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-16 21:08:01,890 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.212093/$0.212121
Spread: 0.013% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.210963 (Distance: 0.50%)
Resistance: $0.213083
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 21:08:01,890 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:08:01,891 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:08:06,516 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 502 chars
2025-07-16 21:08:06,517 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "regime": "RANGING_TIGHT",
  "confidence": 85,
  "scalpability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "MEDIUM",
  "reasoning": "The current regime is classified as RANGING_TIGHT with a neutral trend and medium volatility, which indicates potential for scalping opportunities...
2025-07-16 21:08:06,517 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 144, Total: 740
2025-07-16 21:08:06,518 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalpability', 'recommended_timeframe', 'risk_level', 'reasoning']
2025-07-16 21:08:06,518 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALPABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-16 21:08:06,519 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-16 21:08:06,519 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-16 21:08:07,797 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 225 chars
2025-07-16 21:08:07,797 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level with a rapid price increase.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-16 21:08:07,798 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 92, Total: 266
2025-07-16 21:08:07,798 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-16 21:08:07,798 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-16 21:08:07,799 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-16 21:08:07,799 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-16 21:08:09,330 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 191 chars
2025-07-16 21:08:09,331 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Low spread and neutral signals suggest a timely entry."
}...
2025-07-16 21:08:09,331 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 87, Total: 929
2025-07-16 21:08:09,331 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:08:09,332 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:08:09,332 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 21:08:09,332 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 21:08:09,333 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 7.45s - 3 prompts executed concurrently
2025-07-16 21:08:09,334 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%) | Final decision made based on LLM votes
2025-07-16 21:08:09,334 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%) | Final decision made based on LLM votes
2025-07-16 21:08:09,334 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 3.3
2025-07-16 21:08:14,102 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $18.35, Required Margin: $0.92, Account Balance: $33.10
2025-07-16 21:08:14,102 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-16 21:08:31,150 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-16 21:08:31,151 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:08:31,151 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:08:31,151 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.212431/$0.212432
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.211258 (Distance: 0.50%)
Resistance: $0.213382
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 21:08:31,152 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:08:31,152 - core.llm_orchestrator - INFO - 🚀 Submitted 2 prompts for parallel execution
2025-07-16 21:08:31,152 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:08:31,152 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-16 21:08:31,153 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.212320 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-16 21:08:31,154 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:08:34,961 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 208 chars
2025-07-16 21:08:34,962 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 78,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Neutral signal with favorable spread and no pending volume confirmation."
}...
2025-07-16 21:08:34,962 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 841, Completion: 89, Total: 930
2025-07-16 21:08:34,963 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:08:34,963 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:08:34,963 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 21:08:34,965 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 21:08:37,834 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 695 chars
2025-07-16 21:08:37,834 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 60%, STOP_LOSS: -40%, EXPLANATION: The DOGE/USDT pair shows a neutral sector momentum and normal volatility with an excellent liquidity spread of less than 0.2%. Given the account's healthy status, it is appropriate to take advantage of this opportunity ...
2025-07-16 21:08:37,834 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 937, Completion: 164, Total: 1101
2025-07-16 21:08:37,835 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-16 21:08:37,836 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 60.0, 'STOP_LOSS': -40.0, 'EXPLANATION': "THE DOGE/USDT PAIR SHOWS A NEUTRAL SECTOR MOMENTUM AND NORMAL VOLATILITY WITH AN EXCELLENT LIQUIDITY SPREAD OF LESS THAN 0.2%. GIVEN THE ACCOUNT'S HEALTHY STATUS, IT IS APPROPRIATE TO TAKE ADVANTAGE OF THIS OPPORTUNITY WHILE ADHERING TO RISK MANAGEMENT PROTOCOLS SET BY THE CONSERVATIVE STRATEGY IN PLACE. THE HISTORICAL CONTEXT INDICATES NO SIGNIFICANT PRICE MOVEMENT OR STRONG SIGNALS THAT WOULD SUGGEST A DIFFERENT ACTION; HOWEVER, WITH HIGH SETUP QUALITY AND ALIGNMENT WITH MOMENTUM CRITERIA, ENTERING AT CURRENT LEVELS SEEMS PRUDENT FOR POTENTIAL GAINS WITHOUT EXCEEDING HEALTH-BASED LIMITS ON ACCOUNT IMPACT.", 'ACTION': 'ENTER_NOW'}
2025-07-16 21:08:37,836 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-16 21:08:37,836 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-16 21:08:37,837 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-16 21:08:37,838 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.69s - 2 prompts executed concurrently
2025-07-16 21:08:37,840 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (100.0%) - Decision based on weighted votes: LONG (100.0%) | Final decision made based on LLM votes
2025-07-16 21:08:37,841 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (100.0%) - Decision based on weighted votes: LONG (100.0%) | Final decision made based on LLM votes
2025-07-16 21:08:37,841 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.7
2025-07-16 21:09:01,166 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-16 21:09:01,168 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:09:01,168 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:09:01,168 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:09:01,168 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-16 21:09:01,169 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:09:01,169 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:09:01,169 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:09:01,169 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-16 21:09:01,169 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-16 21:09:01,170 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:09:01,170 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:09:01,171 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.212551/$0.212552
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.211287 (Distance: 0.50%)
Resistance: $0.213411
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 21:09:01,172 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:09:05,753 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 399 chars
2025-07-16 21:09:05,754 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalp_suitability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "MEDIUM",
  "reasoning": "Dynamic regime detection based on volatility and trend analysis indicates a medium suitability for scalping in the current RANGING_TI...
2025-07-16 21:09:05,755 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 125, Total: 721
2025-07-16 21:09:05,756 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level', 'reasoning']
2025-07-16 21:09:05,756 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-16 21:09:05,757 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-16 21:09:05,758 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-16 21:09:07,126 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 239 chars
2025-07-16 21:09:07,127 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above resistance level with high volume and strong momentum indicators.",
  "take_profit": 2.5,
  "stop_loss": 1.0,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-16 21:09:07,127 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 95, Total: 269
2025-07-16 21:09:07,128 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-16 21:09:07,129 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-16 21:09:07,129 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-16 21:09:07,130 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-16 21:09:08,672 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 179 chars
2025-07-16 21:09:08,672 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 75,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Price near key levels with favorable spread"
}...
2025-07-16 21:09:08,673 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 83, Total: 925
2025-07-16 21:09:08,674 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:09:08,674 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:09:08,675 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 21:09:08,675 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 21:09:08,678 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 7.51s - 3 prompts executed concurrently
2025-07-16 21:09:08,679 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%) | Final decision made based on LLM votes
2025-07-16 21:09:08,679 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%) | Final decision made based on LLM votes
2025-07-16 21:09:08,680 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.9, WAIT: 3.3
2025-07-16 21:09:13,521 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $17.82, Required Margin: $0.89, Account Balance: $32.15
2025-07-16 21:09:13,522 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-16 21:09:31,161 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-16 21:09:31,163 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:09:31,164 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:09:31,165 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.212659/$0.212660
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.211439 (Distance: 0.50%)
Resistance: $0.213565
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 21:09:31,165 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:09:31,165 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-16 21:09:31,166 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:09:31,166 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:09:31,166 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:09:31,167 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-16 21:09:31,168 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-16 21:09:31,169 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.212502 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-16 21:09:31,169 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:09:31,170 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:09:35,157 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 200 chars
2025-07-16 21:09:35,157 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 78,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Momentum neutral but spread favorable for potential quick entry."
}...
2025-07-16 21:09:35,158 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 87, Total: 929
2025-07-16 21:09:35,158 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:09:35,160 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:09:35,160 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 21:09:35,161 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 21:09:36,881 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 326 chars
2025-07-16 21:09:36,882 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.5, "hold_time_target":"4min", "entry_threshold":65, "exit_threshold":55, "sizing_method":"VARIABLE_RISK", "reasoning":"Increase risk slightly to capitalize on recent poor performance and adjust thresholds for more aggressive scalping. Reduce hold time due to downward trend in wi...
2025-07-16 21:09:36,882 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 104, Total: 723
2025-07-16 21:09:36,883 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning', 'confidence']
2025-07-16 21:09:36,883 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-16 21:09:36,883 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.5x
2025-07-16 21:09:36,884 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.5x, Hold time 8min
2025-07-16 21:09:36,884 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
2025-07-16 21:09:41,376 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 1183 chars
2025-07-16 21:09:41,377 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: SHORT, CONFIDENCE: 70%, TAKE_PROFIT: 30%, STOP_LOSS: 40%, EXPLANATION: The current market sentiment for DOGE/USDT is neutral with no significant momentum. Given the account's healthy status and conservative risk parameters, a short position could be considered if we anticipate an overreact...
2025-07-16 21:09:41,377 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 938, Completion: 265, Total: 1203
2025-07-16 21:09:41,378 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-16 21:09:41,378 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'SHORT', 'CONFIDENCE': 70.0, 'TAKE_PROFIT': 30.0, 'STOP_LOSS': 40.0, 'EXPLANATION': "THE CURRENT MARKET SENTIMENT FOR DOGE/USDT IS NEUTRAL WITH NO SIGNIFICANT MOMENTUM. GIVEN THE ACCOUNT'S HEALTHY STATUS AND CONSERVATIVE RISK PARAMETERS, A SHORT POSITION COULD BE CONSIDERED IF WE ANTICIPATE AN OVERREACTION IN PRICE DUE TO POTENTIAL NEWS OR SOCIAL MEDIA INFLUENCE THAT MIGHT NOT ALIGN WITH FUNDAMENTAL VALUE INDICATORS SUCH AS VOLUME PROFILE BEING AVERAGE BUT WITHOUT CLEAR PATTERNS OF INCREASED VOLATILITY WHICH WOULD JUSTIFY HIGHER CONFIDENCE LEVELS. THE TAKE PROFIT IS SET AT 30% ABOVE THE ENTRY POINT, CONSIDERING A MODERATE RISK ENVIRONMENT AND ACCOUNT HEALTH-ADJUSTED CRITERIA TO ENSURE WE DO NOT EXCEED OUR MAXIMUM EXPOSURE LIMIT WHILE STILL AIMING FOR AN ATTRACTIVE REWARD RATIO THAT ALIGNS WITH MARKET CONDITIONS BEING NORMAL IN VOLATILITY BUT WITHOUT AGGRESSIVE MOMENTUM INDICATORS. THE STOP LOSS IS SET AT 40% BELOW THE ENTRY POINT AS A PRECAUTIONARY MEASURE AGAINST SUDDEN PRICE DROPS, WHICH COULD OCCUR EVEN WITHIN HEALTHY ACCOUNT STATUSES DUE TO UNFORESEEN EVENTS OR SHIFTS IN MARKET SENTIMENT THAT ARE NOT REFLECTED BY CURRENT DATA POINTS AND HISTORICAL CONTEXT PROVIDED.", 'ACTION': 'ENTER_NOW'}
2025-07-16 21:09:41,378 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-16 21:09:41,379 - core.llm_response_parsers - INFO - Opportunity scanner parsed: MOMENTUM (REVERSAL)
2025-07-16 21:09:41,379 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-16 21:09:41,381 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 10.22s - 3 prompts executed concurrently
2025-07-16 21:09:41,382 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (54.9%) - Decision based on weighted votes: LONG (54.9%) | Final decision made based on LLM votes
2025-07-16 21:09:41,382 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (54.9%) - Decision based on weighted votes: LONG (54.9%) | Final decision made based on LLM votes
2025-07-16 21:09:41,382 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 2.0, SHORT: 1.4, WAIT: 0.2
2025-07-16 21:09:41,383 - core.llm_orchestrator - WARNING - ⚠️ Conflicting signals detected - consensus: 54.9%
2025-07-16 21:10:01,232 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-16 21:10:01,233 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:10:01,234 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:10:01,234 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:10:01,234 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-16 21:10:01,235 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:10:01,235 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:10:01,235 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-16 21:10:01,235 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:10:01,236 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-16 21:10:01,236 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:10:01,237 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:10:01,239 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.212675/$0.212676
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.211534 (Distance: 0.50%)
Resistance: $0.213660
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 21:10:01,240 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:10:05,144 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 165 chars
2025-07-16 21:10:05,145 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
    "REGIME": "RANGING_TIGHT",
    "CONFIDENCE": 75,
    "SCALP_SUITABILITY": "MEDIUM",
    "RECOMMENDED_TIMEFRAME": "1m",
    "RISK_LEVEL": "MEDIUM"
}
```...
2025-07-16 21:10:05,145 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 88, Total: 684
2025-07-16 21:10:05,146 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-16 21:10:05,147 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL']
2025-07-16 21:10:05,147 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-16 21:10:05,147 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-16 21:10:06,682 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 179 chars
2025-07-16 21:10:06,683 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 80,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Price near key levels with favorable spread"
}...
2025-07-16 21:10:06,683 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 83, Total: 925
2025-07-16 21:10:06,684 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:10:06,684 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:10:06,684 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 21:10:06,685 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 21:10:08,183 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 254 chars
2025-07-16 21:10:08,184 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Breakout above the resistance level at $135 with volume spike indicating strong buying interest.",
  "take_profit": 2.5,
  "stop_loss": 1.7,
  "hold_time": "30s",
  "leverage": 40
}
```...
2025-07-16 21:10:08,184 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 103, Total: 277
2025-07-16 21:10:08,185 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-16 21:10:08,185 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-16 21:10:08,186 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-16 21:10:08,186 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-16 21:10:08,187 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 6.96s - 3 prompts executed concurrently
2025-07-16 21:10:08,189 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%) | Final decision made based on LLM votes
2025-07-16 21:10:08,189 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%) | Final decision made based on LLM votes
2025-07-16 21:10:08,189 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 2.0, WAIT: 3.3
2025-07-16 21:10:13,051 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $16.97, Required Margin: $0.85, Account Balance: $30.61
2025-07-16 21:10:13,051 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-16 21:10:31,237 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-16 21:10:31,238 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:10:31,239 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:10:31,239 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:10:31,240 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-16 21:10:31,240 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-16 21:10:31,241 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:10:31,241 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:10:31,242 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:10:31,243 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-16 21:10:31,243 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.212762/$0.212763
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.211637 (Distance: 0.50%)
Resistance: $0.213763
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 21:10:31,245 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.212700 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-16 21:10:31,247 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:10:31,247 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:10:36,104 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 514 chars
2025-07-16 21:10:36,105 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalp_suitability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "MEDIUM",
  "reasoning": "Dynamic regime detection based on volatility and trend analysis indicates a medium suitability for scalping in the current RANGING_TIGHT mark...
2025-07-16 21:10:36,105 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 155, Total: 751
2025-07-16 21:10:36,106 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level', 'reasoning']
2025-07-16 21:10:36,106 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-16 21:10:36,106 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-16 21:10:36,107 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-16 21:10:37,788 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 227 chars
2025-07-16 21:10:37,788 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 68,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 0,
  "REASONING": "Neutral signals but favorable spread and basic level calculation suggest a potential entry."
}...
2025-07-16 21:10:37,789 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 840, Completion: 91, Total: 931
2025-07-16 21:10:37,789 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:10:37,789 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:10:37,790 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 21:10:37,790 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 21:10:40,964 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 697 chars
2025-07-16 21:10:40,965 - llama.lmstudio_runner - INFO - 📄 Response Preview: DECISION: LONG, CONFIDENCE: 85%, TAKE_PROFIT: 30%, STOP_LOSS: -40%, EXPLANATION: The DOGE/USDT pair shows a slight positive momentum with no significant changes in volatility and maintains an excellent liquidity spread. Given the account's healthy status, normal trading parameters are acceptable for...
2025-07-16 21:10:40,965 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 936, Completion: 177, Total: 1113
2025-07-16 21:10:40,966 - core.llm_response_parsers - INFO - 🔄 No JSON found, trying structured text parsing
2025-07-16 21:10:40,966 - core.llm_response_parsers - INFO - Parsed structured text: {'DECISION': 'LONG', 'CONFIDENCE': 85.0, 'TAKE_PROFIT': 30.0, 'STOP_LOSS': -40.0, 'EXPLANATION': "THE DOGE/USDT PAIR SHOWS A SLIGHT POSITIVE MOMENTUM WITH NO SIGNIFICANT CHANGES IN VOLATILITY AND MAINTAINS AN EXCELLENT LIQUIDITY SPREAD. GIVEN THE ACCOUNT'S HEALTHY STATUS, NORMAL TRADING PARAMETERS ARE ACCEPTABLE FOR THIS CONSERVATIVE ENTRY POSITION ON DOGECOIN (DOGE). A 30% TAKE-PROFIT TARGET IS SET TO CAPITALIZE ON POTENTIAL GAINS WHILE A STOP LOSS OF -40% ENSURES RISK MANAGEMENT ALIGNS WITH OUR MODERATE ACCOUNT PRESERVATION CRITERIA. THE CONFIDENCE LEVEL REFLECTS THE STABILITY IN PRICE AND SIGNAL STRENGTH, INDICATING AN INTELLIGENT OPPORTUNITY FOR LONG POSITIONING WITHIN CONSERVATIVE TRADING GUIDELINES.", 'ACTION': 'ENTER_NOW'}
2025-07-16 21:10:40,967 - core.llm_response_parsers - INFO - ✅ Structured text parsing successful: ['DECISION', 'CONFIDENCE', 'TAKE_PROFIT', 'STOP_LOSS', 'EXPLANATION', 'ACTION']
2025-07-16 21:10:40,967 - core.llm_response_parsers - INFO - Opportunity scanner parsed: BREAKOUT (MOMENTUM)
2025-07-16 21:10:40,967 - core.llm_orchestrator - INFO - ✅ Completed prompt: opportunity_scanner
2025-07-16 21:10:40,969 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 9.73s - 3 prompts executed concurrently
2025-07-16 21:10:40,971 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: LONG (85.8%) - Decision based on weighted votes: LONG (85.8%) | Final decision made based on LLM votes
2025-07-16 21:10:40,971 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: LONG (85.8%) - Decision based on weighted votes: LONG (85.8%) | Final decision made based on LLM votes
2025-07-16 21:10:40,972 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 3.4, WAIT: 0.6
2025-07-16 21:11:01,307 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-16 21:11:01,309 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:11:01,309 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:11:01,310 - llama.lmstudio_runner - INFO - 💬 User Prompt: 📊 MARKET REGIME ANALYST - DOGE/USDT:USDT

🔍 MULTI-TIMEFRAME ANALYSIS:
1m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
5m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%
15m: Trend=NEUTRAL | Vol=1.0x | Mom=+0.0%

📈 VOLATILITY METRICS:
ATR: 0.001000 | Spread: 0.000%
Volume Ratio: 1.0x | Price Range: 1.00%

🎯 REGIME CLASSIFICATION:
Current Regime: RANGING_TIGHT
Trend: NEUTRAL (Strength: 0.0%)
Volatility: MEDIUM | Volume: NORMAL

⚡ SCALPING SUITABILITY: MEDIUM
Risk Adjustments: Leverage 0.6x | Position 0.8x

REGIME ...
2025-07-16 21:11:01,310 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:11:01,310 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:11:01,310 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:11:01,310 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:11:01,311 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-16 21:11:01,311 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:11:01,311 - llama.lmstudio_runner - INFO - 💬 User Prompt: ...
2025-07-16 21:11:01,313 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.212700/$0.212701
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.211741 (Distance: 0.50%)
Resistance: $0.213869
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 21:11:01,313 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:11:01,313 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:11:05,240 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 237 chars
2025-07-16 21:11:05,241 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "regime": "RANGING_TIGHT",
  "confidence": 75,
  "scalp_suitability": "MEDIUM",
  "recommended_timeframe": "1m",
  "risk_level": "MEDIUM",
  "reasoning": "Dynamic regime detection based on volatility and trend analysis"
}
```...
2025-07-16 21:11:05,241 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 596, Completion: 93, Total: 689
2025-07-16 21:11:05,242 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['regime', 'confidence', 'scalp_suitability', 'recommended_timeframe', 'risk_level', 'reasoning']
2025-07-16 21:11:05,243 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['REGIME', 'CONFIDENCE', 'SCALP_SUITABILITY', 'RECOMMENDED_TIMEFRAME', 'RISK_LEVEL', 'REASONING']
2025-07-16 21:11:05,243 - core.llm_response_parsers - INFO - Market regime parsed: RANGING_TIGHT (MEDIUM scalp suitability)
2025-07-16 21:11:05,244 - core.llm_orchestrator - INFO - ✅ Completed prompt: market_regime
2025-07-16 21:11:06,968 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 200 chars
2025-07-16 21:11:06,969 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 70,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Neutral signal but favorable spread and key levels near support"
}...
2025-07-16 21:11:06,969 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 89, Total: 931
2025-07-16 21:11:06,970 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:11:06,971 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:11:06,971 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 21:11:06,972 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 21:11:08,515 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 235 chars
2025-07-16 21:11:08,516 - llama.lmstudio_runner - INFO - 📄 Response Preview: ```json
{
  "action": "LONG",
  "confidence": 90,
  "entry_reason": "Market broke through resistance level at $1350 with a sudden spike to $1362.",
  "take_profit": 2.8,
  "stop_loss": 1.4,
  "hold_time": "3min",
  "leverage": 40
}
```...
2025-07-16 21:11:08,516 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 174, Completion: 104, Total: 278
2025-07-16 21:11:08,517 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['action', 'confidence', 'entry_reason', 'take_profit', 'stop_loss', 'hold_time', 'leverage']
2025-07-16 21:11:08,517 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'CONFIDENCE', 'ENTRY_REASON', 'TAKE_PROFIT', 'STOP_LOSS', 'HOLD_TIME', 'LEVERAGE']
2025-07-16 21:11:08,518 - core.llm_response_parsers - INFO - Risk assessment parsed: REJECTED (Risk: 50)
2025-07-16 21:11:08,518 - core.llm_orchestrator - INFO - ✅ Completed prompt: risk_assessment
2025-07-16 21:11:08,520 - core.llm_orchestrator - INFO - 🚀 PARALLEL LLM cycle completed in 7.21s - 3 prompts executed concurrently
2025-07-16 21:11:08,521 - core.vote_aggregator - INFO - 🗳️ Vote aggregation: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%) | Final decision made based on LLM votes
2025-07-16 21:11:08,522 - core.llm_orchestrator - INFO - 🗳️ Aggregated Decision: WAIT (50.0%) - Decision based on weighted votes: WAIT (50.0%) | Final decision made based on LLM votes
2025-07-16 21:11:08,522 - core.llm_orchestrator - INFO - 🗳️ Vote Breakdown: LONG: 1.8, WAIT: 3.3
2025-07-16 21:11:13,526 - core.risk_management_system - INFO - 🔍 Risk Check: Position Notional: $15.29, Required Margin: $0.76, Account Balance: $27.64
2025-07-16 21:11:13,527 - core.risk_management_system - INFO - 🔍 Risk Check: Margin Usage: 2.8% (Limit: 10.0%)
2025-07-16 21:11:31,297 - core.llm_orchestrator - INFO - 🔍 FULL MODE: Executing all 8 prompts in PARALLEL for comprehensive analysis
2025-07-16 21:11:31,298 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:11:31,298 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:11:31,298 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🎯 ENTRY TIMING SPECIALIST - DOGE/USDT:USDT

🎯 SIGNAL ANALYSIS:
ML Ensemble: NEUTRAL (50.0%)
Technical: NEUTRAL | Momentum: 0.00
Volume: PENDING | Orderflow: NEUTRAL

📊 MARKET MICROSTRUCTURE:
Bid/Ask: $0.212800/$0.212801
Spread: 0.000% | L2 Imbalance: 0.0%
Recent Flow: BALANCED | Volume Spike: False

📈 KEY LEVELS (DYNAMIC):
Support: $0.211672 (Distance: 0.50%)
Resistance: $0.213800
Position: BASIC_CALCULATION | Action: NEUTRAL

⏰ TIMING FACTORS:
- Price near key levels: NEUTRAL
- Volume confirmat...
2025-07-16 21:11:31,299 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:11:31,299 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:11:31,299 - llama.lmstudio_runner - INFO - 📝 System Prompt: You are a conservative futures trading analyst. Analyze market data carefully and provide measured recommendations. Use format: DECISION: [LONG/SHORT/WAIT], CONFIDENCE: [50-100]%, TAKE_PROFIT: [%], ST...
2025-07-16 21:11:31,299 - llama.lmstudio_runner - INFO - 🤖 LMStudio Request - Model: phi-3.1-mini-128k-instruct
2025-07-16 21:11:31,300 - core.llm_orchestrator - INFO - 🚀 Submitted 3 prompts for parallel execution
2025-07-16 21:11:31,300 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔍 INTELLIGENT OPPORTUNITY SCANNER - Account Health Aware

🚨 ACCOUNT HEALTH STATUS: HEALTHY
💰 Balance: $1000.00 | Health: Healthy - Normal Trading
📊 Current Exposure: $0.00 (0.0% of account)
🎯 Trading Allowed: YES

📊 SYMBOL ANALYSIS:
• DOGE/USDT:USDT: $0.212736 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• BTC/USDT:USDT: $95000.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• ETH/USDT:USDT: $3500.000000 | Mom: +0.0% | Vol: 1.0x | Setup: MEDIUM
• SOL/USDT:USDT: $200.000000 | Mom: +0.0% | Vol: 1.0x |...
2025-07-16 21:11:31,300 - llama.lmstudio_runner - INFO - 📝 System Prompt: ELITE FUTURES SCALPER: You are a high-frequency scalper. NEVER WAIT unless market broken. Target 2-15 pip moves, 10x-50x leverage, 30s-5min holds. BIAS TOWARD ACTION. Output JSON: {"action":"LONG/SHOR...
2025-07-16 21:11:31,301 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:11:31,302 - llama.lmstudio_runner - INFO - 💬 User Prompt: 🔄 STRATEGY ADAPTATION SPECIALIST

📊 PERFORMANCE ANALYSIS (Last 24h):
Trades: 0 | Win Rate: 50.0%
Avg Profit: 0.80% | Avg Loss: -0.30%
Sharpe Ratio: 1.00 | Max Drawdown: 0.0%
Total PnL: $0.00 | ROI: 0.0%

🎯 CURRENT STRATEGY:
Risk per Trade: 2.0% | Avg Hold Time: 8.0min
Entry Threshold: 70% | Exit Threshold: 60%
Position Size Method: FIXED_RISK | Max Positions: 3

📈 MARKET REGIME: UNKNOWN
Regime Confidence: 50.0%
Scalp Suitability: MEDIUM

🔧 ADAPTATION FACTORS:
- Win rate trending: DOWN
- Drawdown...
2025-07-16 21:11:31,302 - llama.lmstudio_runner - INFO - ⚙️ Parameters - Temperature: 0.3, Max Tokens: 768
2025-07-16 21:11:35,042 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 174 chars
2025-07-16 21:11:35,043 - llama.lmstudio_runner - INFO - 📄 Response Preview: {
  "ACTION": "ENTER_NOW",
  "ENTRY_TYPE": "LIMIT",
  "CONFIDENCE": 80,
  "WAIT_FOR": null,
  "MAX_WAIT_SECONDS": 30,
  "REASONING": "Spread favorable and momentum neutral"
}...
2025-07-16 21:11:35,043 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 842, Completion: 83, Total: 925
2025-07-16 21:11:35,044 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:11:35,045 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['ACTION', 'ENTRY_TYPE', 'CONFIDENCE', 'WAIT_FOR', 'MAX_WAIT_SECONDS', 'REASONING']
2025-07-16 21:11:35,045 - core.llm_response_parsers - INFO - Entry timing parsed: ENTER_NOW (LIMIT order)
2025-07-16 21:11:35,046 - core.llm_orchestrator - INFO - ✅ Completed prompt: entry_timing
2025-07-16 21:11:36,400 - llama.lmstudio_runner - INFO - ✅ LMStudio Response - Length: 254 chars
2025-07-16 21:11:36,400 - llama.lmstudio_runner - INFO - 📄 Response Preview: {"risk_adjustment":1.5, "hold_time_target":30, "entry_threshold":80, "exit_threshold":70, "sizing_method":"VARIABLE_RISK", "reasoning":"Increase risk slightly due to high confidence in regime change; shorter hold time for quicker scalping opportunities"}...
2025-07-16 21:11:36,400 - llama.lmstudio_runner - INFO - 🔢 Token Usage - Prompt: 619, Completion: 82, Total: 701
2025-07-16 21:11:36,401 - core.llm_response_parsers - INFO - ✅ JSON parsing successful: ['risk_adjustment', 'hold_time_target', 'entry_threshold', 'exit_threshold', 'sizing_method', 'reasoning']
2025-07-16 21:11:36,401 - core.llm_response_parsers - INFO - 🎯 Normalized JSON result: ['RISK_ADJUSTMENT', 'HOLD_TIME_TARGET', 'ENTRY_THRESHOLD', 'EXIT_THRESHOLD', 'SIZING_METHOD', 'REASONING', 'CONFIDENCE']
2025-07-16 21:11:36,402 - core.llm_response_parsers - INFO - Strategy adaptation parsed: Risk adj 1.5x
2025-07-16 21:11:36,402 - core.llm_action_executors - INFO - Applying strategy adaptations: Risk 1.5x, Hold time 30min
2025-07-16 21:11:36,402 - core.llm_orchestrator - INFO - ✅ Completed prompt: strategy_adaptation
