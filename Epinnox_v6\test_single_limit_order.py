#!/usr/bin/env python3
"""
Minimal Test Script for Single LIMIT Order Execution
Tests the basic trading execution pipeline with minimal complexity
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import required modules
from trading.ccxt_trading_engine import CCXTTradingEngine
from trading.real_trading_interface import RealTradingInterface
from credentials import CredentialsManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MinimalTradingTest:
    """Minimal trading test class"""
    
    def __init__(self):
        self.trading_engine = None
        self.trading_interface = None
        
    def initialize(self):
        """Initialize trading components"""
        try:
            logger.info("🚀 Initializing minimal trading test...")
            
            # Initialize trading engine with live trading mode
            # The engine will automatically load credentials from the config system
            self.trading_engine = CCXTTradingEngine(
                exchange_name="htx",
                demo_mode=False
            )
            
            # Initialize trading interface
            self.trading_interface = RealTradingInterface(self.trading_engine)
            
            logger.info("✅ Trading components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize: {e}")
            return False
    
    def test_balance_retrieval(self):
        """Test balance retrieval"""
        try:
            logger.info("🔍 Testing balance retrieval...")
            
            balance = self.trading_engine.get_balance()
            if balance:
                usdt_balance = balance.get('USDT', {}).get('free', 0)
                logger.info(f"✅ Balance retrieved: ${usdt_balance:.2f} USDT")
                return usdt_balance
            else:
                logger.error("❌ Failed to retrieve balance")
                return 0
                
        except Exception as e:
            logger.error(f"❌ Error retrieving balance: {e}")
            return 0
    
    def test_orderbook_retrieval(self, symbol="DOGE/USDT:USDT"):
        """Test order book retrieval"""
        try:
            logger.info(f"🔍 Testing order book retrieval for {symbol}...")
            
            orderbook = self.trading_engine.get_orderbook(symbol)
            if orderbook and orderbook.get('bids') and orderbook.get('asks'):
                best_bid = orderbook['bids'][0][0]
                best_ask = orderbook['asks'][0][0]
                spread = ((best_ask - best_bid) / best_bid) * 100
                
                logger.info(f"✅ Order book retrieved:")
                logger.info(f"   Best bid: ${best_bid:.6f}")
                logger.info(f"   Best ask: ${best_ask:.6f}")
                logger.info(f"   Spread: {spread:.3f}%")
                
                return {'bid': best_bid, 'ask': best_ask, 'spread': spread}
            else:
                logger.error("❌ Failed to retrieve order book")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error retrieving order book: {e}")
            return None
    
    def test_htx_conversion(self, doge_amount=100.0):
        """Test HTX contract conversion"""
        try:
            logger.info(f"🔍 Testing HTX conversion for {doge_amount} DOGE...")
            
            # Test the conversion method
            contracts = self.trading_interface.convert_to_htx_contract_format("DOGE/USDT:USDT", doge_amount)
            
            logger.info(f"✅ HTX conversion: {doge_amount} DOGE → {contracts} contracts")
            logger.info(f"   Equivalent: {contracts * 100:.0f} DOGE")
            
            return contracts
            
        except Exception as e:
            logger.error(f"❌ Error in HTX conversion: {e}")
            return None
    
    def test_single_limit_order(self, symbol="DOGE/USDT:USDT", side="buy", amount=1.0):
        """Test placing a single LIMIT order"""
        try:
            logger.info(f"🎯 Testing single LIMIT order: {side.upper()} {amount} {symbol}")
            
            # Get current order book for pricing
            orderbook = self.test_orderbook_retrieval(symbol)
            if not orderbook:
                logger.error("❌ Cannot place order without order book data")
                return False
            
            # Calculate limit price (slightly aggressive for quick execution)
            if side.lower() == 'buy':
                # Buy at ask price for immediate execution
                limit_price = orderbook['ask']
                logger.info(f"🎯 Using aggressive BUY price: ${limit_price:.6f}")
            else:
                # Sell at bid price for immediate execution
                limit_price = orderbook['bid']
                logger.info(f"🎯 Using aggressive SELL price: ${limit_price:.6f}")
            
            # Place the limit order
            logger.info(f"🚀 Placing LIMIT order: {side.upper()} {amount} {symbol} @ ${limit_price:.6f}")
            
            order = self.trading_engine.place_limit_order(
                symbol=symbol,
                side=side,
                amount=amount,
                price=limit_price,
                params={
                    'offset': 'open',
                    'lever_rate': 20,
                    'marginMode': 'cross'
                }
            )
            
            if order:
                logger.info(f"✅ LIMIT order placed successfully!")
                logger.info(f"   Order ID: {order.get('id', 'NO_ID')}")
                logger.info(f"   Status: {order.get('status', 'UNKNOWN')}")
                return True
            else:
                logger.error("❌ LIMIT order placement failed - trading engine returned None")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error placing LIMIT order: {e}")
            return False
    
    def run_comprehensive_test(self):
        """Run comprehensive test suite"""
        try:
            logger.info("🧪 Starting comprehensive trading test...")
            
            # Test 1: Initialize
            if not self.initialize():
                return False
            
            # Test 2: Balance
            balance = self.test_balance_retrieval()
            if balance <= 0:
                logger.error("❌ Insufficient balance for testing")
                return False
            
            # Test 3: Order book
            orderbook = self.test_orderbook_retrieval()
            if not orderbook:
                return False
            
            # Test 4: HTX conversion
            contracts = self.test_htx_conversion(100.0)
            if contracts is None:
                return False
            
            # Test 5: Single LIMIT order (1 contract = 100 DOGE)
            logger.info("🎯 Final test: Placing 1 contract LIMIT order...")
            success = self.test_single_limit_order("DOGE/USDT:USDT", "buy", 1.0)
            
            if success:
                logger.info("🎉 ALL TESTS PASSED! Trading execution pipeline is working.")
                return True
            else:
                logger.error("❌ LIMIT order test failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Comprehensive test failed: {e}")
            return False

def main():
    """Main test function"""
    print("=" * 60)
    print("🧪 MINIMAL TRADING EXECUTION TEST")
    print("=" * 60)
    
    test = MinimalTradingTest()
    success = test.run_comprehensive_test()
    
    print("=" * 60)
    if success:
        print("🎉 TEST RESULT: SUCCESS - Trading pipeline is functional")
    else:
        print("❌ TEST RESULT: FAILED - Trading pipeline needs fixes")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
