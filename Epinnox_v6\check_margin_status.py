#!/usr/bin/env python3
"""
Check Current Margin Status and Positions
Diagnose margin calculation issues
"""

import sys
import os
import logging

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import required modules
from trading.ccxt_trading_engine import CCXTTradingEngine
from credentials import CredentialsManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_margin_status():
    """Check current margin status and positions"""
    try:
        print("=" * 60)
        print("🔍 MARGIN STATUS DIAGNOSTIC")
        print("=" * 60)
        
        # Initialize trading engine
        trading_engine = CCXTTradingEngine(exchange_name="htx", demo_mode=False)
        
        # 1. Check account balance
        print("\n📊 ACCOUNT BALANCE:")
        balance = trading_engine.get_balance()
        if balance:
            usdt_balance = balance.get('USDT', {})
            free_balance = usdt_balance.get('free', 0)
            used_balance = usdt_balance.get('used', 0)
            total_balance = usdt_balance.get('total', 0)
            
            print(f"   Free Balance: ${free_balance:.2f}")
            print(f"   Used Balance: ${used_balance:.2f}")
            print(f"   Total Balance: ${total_balance:.2f}")
        else:
            print("   ❌ Failed to retrieve balance")
            return
        
        # 2. Check all positions
        print("\n📈 OPEN POSITIONS:")
        positions = trading_engine.get_all_positions()
        total_margin_used = 0

        if not positions:
            print("   ✅ No open positions")
        else:
            for symbol, position in positions.items():
                print(f"\n   Symbol: {symbol}")
                print(f"   Side: {position.get('side', 'N/A')}")
                print(f"   Size: {position.get('size', 0)} / {position.get('contracts', 0)}")
                print(f"   Entry Price: ${position.get('entryPrice', 0):.6f}")
                print(f"   Mark Price: ${position.get('markPrice', 0):.6f}")
                print(f"   Leverage: {position.get('leverage', 'N/A')}x")
                print(f"   Notional: ${position.get('notional', 0):.2f}")
                print(f"   Unrealized PnL: ${position.get('unrealizedPnl', 0):.2f}")
                
                # Calculate margin used
                notional = float(position.get('notional', 0))
                leverage = float(position.get('leverage', 1))
                if leverage > 0:
                    margin_used = notional / leverage
                    total_margin_used += margin_used
                    print(f"   Margin Used: ${margin_used:.2f}")
        
        # 3. Calculate available margin
        print(f"\n💰 MARGIN SUMMARY:")
        print(f"   Total Margin Used: ${total_margin_used:.2f}")
        print(f"   Available Margin: ${free_balance:.2f}")
        print(f"   Margin Utilization: {(total_margin_used / total_balance * 100):.1f}%")
        
        # 4. Test DOGE position calculation
        print(f"\n🧮 DOGE POSITION TEST:")
        doge_price = 0.214  # Approximate current price
        test_contracts = 1.02  # From the error logs
        test_doge = test_contracts * 100  # 102 DOGE
        test_notional = test_doge * doge_price  # $21.86
        
        # Test with different leverages
        for leverage in [20, 75]:
            required_margin = test_notional / leverage
            print(f"   {leverage}x Leverage:")
            print(f"     Position: {test_contracts:.2f} contracts = {test_doge:.0f} DOGE")
            print(f"     Notional: ${test_notional:.2f}")
            print(f"     Required Margin: ${required_margin:.2f}")
            print(f"     Available: ${free_balance:.2f}")
            print(f"     Status: {'✅ OK' if required_margin <= free_balance else '❌ INSUFFICIENT'}")
        
        # 5. Check existing DOGE position leverage
        print(f"\n🔍 DOGE POSITION LEVERAGE CHECK:")
        existing_leverage = trading_engine.get_existing_position_leverage('DOGE/USDT:USDT')
        print(f"   Existing DOGE Leverage: {existing_leverage}x")
        
        print("\n" + "=" * 60)
        
    except Exception as e:
        print(f"❌ Error checking margin status: {e}")

if __name__ == "__main__":
    check_margin_status()
