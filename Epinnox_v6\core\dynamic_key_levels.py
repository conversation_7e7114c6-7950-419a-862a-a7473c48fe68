#!/usr/bin/env python3
"""
Dynamic Key Levels Calculation System
Calculates comprehensive support/resistance levels for trading decisions
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import math

logger = logging.getLogger(__name__)

class LevelType(Enum):
    """Types of key levels"""
    PIVOT_POINT = "pivot_point"
    FIBONACCI = "fibonacci"
    VOLUME_PROFILE = "volume_profile"
    PSYCHOLOGICAL = "psychological"
    DYNAMIC_SR = "dynamic_sr"
    PREVIOUS_LEVELS = "previous_levels"

class LevelStrength(Enum):
    """Strength of key levels"""
    WEAK = 1
    MODERATE = 2
    STRONG = 3
    CRITICAL = 4

@dataclass
class KeyLevel:
    """Individual key level data"""
    price: float
    level_type: LevelType
    strength: LevelStrength
    description: str
    distance_pct: float = 0.0
    volume_at_level: float = 0.0
    touches: int = 0
    last_test_time: Optional[str] = None

@dataclass
class KeyLevelsResult:
    """Complete key levels analysis result"""
    current_price: float
    symbol: str
    timestamp: str
    
    # Categorized levels
    support_levels: List[KeyLevel]
    resistance_levels: List[KeyLevel]
    
    # Nearest levels
    nearest_support: KeyLevel
    nearest_resistance: KeyLevel
    
    # Level analysis
    price_position: str  # "BETWEEN_LEVELS", "AT_SUPPORT", "AT_RESISTANCE", "BREAKOUT"
    level_density: float  # Number of levels per price range
    confluence_zones: List[Dict[str, Any]]  # Areas with multiple level types
    
    # Trading implications
    support_strength: float  # 0-100 score
    resistance_strength: float  # 0-100 score
    breakout_probability: float  # 0-100 probability
    recommended_action: str  # "BUY_SUPPORT", "SELL_RESISTANCE", "WAIT_BREAKOUT", "NEUTRAL"

class DynamicKeyLevelsCalculator:
    """🚨 CRITICAL: Dynamic Key Levels Calculation System"""
    
    def __init__(self):
        self.fibonacci_ratios = [0.236, 0.382, 0.5, 0.618, 0.786, 1.0, 1.272, 1.414, 1.618, 2.0]
        self.psychological_levels = [0.1, 0.2, 0.25, 0.5, 0.75, 1.0]  # Multipliers for round numbers
        
    def calculate_key_levels(self, 
                           market_data: pd.DataFrame, 
                           current_price: float, 
                           symbol: str,
                           lookback_periods: int = 100) -> KeyLevelsResult:
        """🚨 CRITICAL: Calculate comprehensive key levels"""
        try:
            logger.info(f"Calculating dynamic key levels for {symbol} at {current_price}")
            
            # Initialize result
            timestamp = pd.Timestamp.now().isoformat()
            
            # Calculate different types of levels
            pivot_levels = self._calculate_pivot_points(market_data, current_price)
            fibonacci_levels = self._calculate_fibonacci_levels(market_data, current_price, lookback_periods)
            volume_levels = self._calculate_volume_profile_levels(market_data, current_price)
            psychological_levels = self._calculate_psychological_levels(current_price)
            dynamic_sr_levels = self._calculate_dynamic_support_resistance(market_data, current_price, lookback_periods)
            previous_levels = self._calculate_previous_levels(market_data, current_price)
            
            # Combine all levels
            all_levels = (pivot_levels + fibonacci_levels + volume_levels + 
                         psychological_levels + dynamic_sr_levels + previous_levels)
            
            # Categorize levels
            support_levels = [level for level in all_levels if level.price < current_price]
            resistance_levels = [level for level in all_levels if level.price > current_price]
            
            # Sort by distance from current price
            support_levels.sort(key=lambda x: x.price, reverse=True)  # Closest first
            resistance_levels.sort(key=lambda x: x.price)  # Closest first
            
            # Find nearest levels
            nearest_support = support_levels[0] if support_levels else self._create_fallback_support(current_price)
            nearest_resistance = resistance_levels[0] if resistance_levels else self._create_fallback_resistance(current_price)
            
            # Analyze price position
            price_position = self._analyze_price_position(current_price, nearest_support, nearest_resistance)
            
            # Calculate confluence zones
            confluence_zones = self._find_confluence_zones(all_levels, current_price)
            
            # Calculate level density
            level_density = self._calculate_level_density(all_levels, current_price)
            
            # Calculate strength scores
            support_strength = self._calculate_level_strength(support_levels[:3])  # Top 3 support levels
            resistance_strength = self._calculate_level_strength(resistance_levels[:3])  # Top 3 resistance levels
            
            # Calculate breakout probability
            breakout_probability = self._calculate_breakout_probability(
                current_price, nearest_support, nearest_resistance, market_data
            )
            
            # Determine recommended action
            recommended_action = self._determine_recommended_action(
                current_price, nearest_support, nearest_resistance, 
                support_strength, resistance_strength, breakout_probability
            )
            
            result = KeyLevelsResult(
                current_price=current_price,
                symbol=symbol,
                timestamp=timestamp,
                support_levels=support_levels[:10],  # Top 10 support levels
                resistance_levels=resistance_levels[:10],  # Top 10 resistance levels
                nearest_support=nearest_support,
                nearest_resistance=nearest_resistance,
                price_position=price_position,
                level_density=level_density,
                confluence_zones=confluence_zones,
                support_strength=support_strength,
                resistance_strength=resistance_strength,
                breakout_probability=breakout_probability,
                recommended_action=recommended_action
            )
            
            logger.info(f"✅ Key levels calculated: {len(support_levels)} support, {len(resistance_levels)} resistance")
            logger.info(f"Nearest support: {nearest_support.price:.6f} ({nearest_support.distance_pct:.2f}%)")
            logger.info(f"Nearest resistance: {nearest_resistance.price:.6f} ({nearest_resistance.distance_pct:.2f}%)")
            logger.info(f"Recommended action: {recommended_action}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error calculating key levels: {e}")
            return self._create_fallback_result(current_price, symbol)
    
    def _calculate_pivot_points(self, market_data: pd.DataFrame, current_price: float) -> List[KeyLevel]:
        """Calculate standard pivot points"""
        try:
            if len(market_data) < 1:
                return []
            
            # Get previous day's data
            prev_high = market_data['high'].iloc[-1] if 'high' in market_data.columns else current_price * 1.01
            prev_low = market_data['low'].iloc[-1] if 'low' in market_data.columns else current_price * 0.99
            prev_close = market_data['close'].iloc[-1] if 'close' in market_data.columns else current_price
            
            # Standard pivot point calculation
            pivot = (prev_high + prev_low + prev_close) / 3
            
            # Support and resistance levels
            r1 = 2 * pivot - prev_low
            r2 = pivot + (prev_high - prev_low)
            r3 = prev_high + 2 * (pivot - prev_low)
            
            s1 = 2 * pivot - prev_high
            s2 = pivot - (prev_high - prev_low)
            s3 = prev_low - 2 * (prev_high - pivot)
            
            levels = []
            
            # Add pivot point
            levels.append(KeyLevel(
                price=pivot,
                level_type=LevelType.PIVOT_POINT,
                strength=LevelStrength.STRONG,
                description="Daily Pivot Point",
                distance_pct=abs(current_price - pivot) / current_price * 100
            ))
            
            # Add resistance levels
            for i, r_level in enumerate([r1, r2, r3], 1):
                levels.append(KeyLevel(
                    price=r_level,
                    level_type=LevelType.PIVOT_POINT,
                    strength=LevelStrength.MODERATE if i <= 2 else LevelStrength.WEAK,
                    description=f"Pivot Resistance R{i}",
                    distance_pct=abs(current_price - r_level) / current_price * 100
                ))
            
            # Add support levels
            for i, s_level in enumerate([s1, s2, s3], 1):
                levels.append(KeyLevel(
                    price=s_level,
                    level_type=LevelType.PIVOT_POINT,
                    strength=LevelStrength.MODERATE if i <= 2 else LevelStrength.WEAK,
                    description=f"Pivot Support S{i}",
                    distance_pct=abs(current_price - s_level) / current_price * 100
                ))
            
            return levels
            
        except Exception as e:
            logger.error(f"Error calculating pivot points: {e}")
            return []
    
    def _calculate_fibonacci_levels(self, market_data: pd.DataFrame, current_price: float, lookback: int) -> List[KeyLevel]:
        """Calculate Fibonacci retracement levels"""
        try:
            if len(market_data) < lookback:
                return []
            
            # Find recent swing high and low
            recent_data = market_data.tail(lookback)
            swing_high = recent_data['high'].max() if 'high' in recent_data.columns else current_price * 1.05
            swing_low = recent_data['low'].min() if 'low' in recent_data.columns else current_price * 0.95
            
            levels = []
            range_size = swing_high - swing_low
            
            # Calculate Fibonacci retracement levels
            for ratio in self.fibonacci_ratios:
                if ratio <= 1.0:  # Retracement levels
                    fib_level = swing_high - (range_size * ratio)
                    levels.append(KeyLevel(
                        price=fib_level,
                        level_type=LevelType.FIBONACCI,
                        strength=LevelStrength.STRONG if ratio in [0.382, 0.5, 0.618] else LevelStrength.MODERATE,
                        description=f"Fibonacci {ratio:.1%} Retracement",
                        distance_pct=abs(current_price - fib_level) / current_price * 100
                    ))
                else:  # Extension levels
                    fib_level = swing_high + (range_size * (ratio - 1.0))
                    levels.append(KeyLevel(
                        price=fib_level,
                        level_type=LevelType.FIBONACCI,
                        strength=LevelStrength.MODERATE,
                        description=f"Fibonacci {ratio:.1%} Extension",
                        distance_pct=abs(current_price - fib_level) / current_price * 100
                    ))
            
            return levels
            
        except Exception as e:
            logger.error(f"Error calculating Fibonacci levels: {e}")
            return []

    def _calculate_volume_profile_levels(self, market_data: pd.DataFrame, current_price: float) -> List[KeyLevel]:
        """Calculate volume profile levels (VWAP, POC, Value Areas)"""
        try:
            if len(market_data) < 20 or 'volume' not in market_data.columns:
                return []

            levels = []

            # Calculate VWAP (Volume Weighted Average Price)
            if 'close' in market_data.columns:
                typical_price = (market_data['high'] + market_data['low'] + market_data['close']) / 3
                vwap = (typical_price * market_data['volume']).sum() / market_data['volume'].sum()

                levels.append(KeyLevel(
                    price=vwap,
                    level_type=LevelType.VOLUME_PROFILE,
                    strength=LevelStrength.STRONG,
                    description="VWAP (Volume Weighted Average Price)",
                    distance_pct=abs(current_price - vwap) / current_price * 100,
                    volume_at_level=market_data['volume'].mean()
                ))

            # Calculate Point of Control (POC) - simplified version
            if 'close' in market_data.columns:
                # Create price bins and calculate volume at each level
                price_range = market_data['high'].max() - market_data['low'].min()
                num_bins = min(50, len(market_data))

                price_bins = np.linspace(market_data['low'].min(), market_data['high'].max(), num_bins)
                volume_profile = np.zeros(len(price_bins) - 1)

                for i, row in market_data.iterrows():
                    # Distribute volume across price range for this candle
                    low_idx = np.searchsorted(price_bins, row['low'])
                    high_idx = np.searchsorted(price_bins, row['high'])

                    if high_idx > low_idx:
                        volume_per_bin = row['volume'] / (high_idx - low_idx)
                        volume_profile[low_idx:high_idx] += volume_per_bin

                # Find Point of Control (highest volume price)
                poc_idx = np.argmax(volume_profile)
                poc_price = (price_bins[poc_idx] + price_bins[poc_idx + 1]) / 2

                levels.append(KeyLevel(
                    price=poc_price,
                    level_type=LevelType.VOLUME_PROFILE,
                    strength=LevelStrength.CRITICAL,
                    description="POC (Point of Control)",
                    distance_pct=abs(current_price - poc_price) / current_price * 100,
                    volume_at_level=volume_profile[poc_idx]
                ))

            return levels

        except Exception as e:
            logger.error(f"Error calculating volume profile levels: {e}")
            return []

    def _calculate_psychological_levels(self, current_price: float) -> List[KeyLevel]:
        """Calculate psychological round number levels"""
        try:
            levels = []

            # Determine the appropriate round number scale
            if current_price >= 1000:
                round_numbers = [100, 250, 500, 1000]
            elif current_price >= 100:
                round_numbers = [10, 25, 50, 100]
            elif current_price >= 10:
                round_numbers = [1, 2.5, 5, 10]
            elif current_price >= 1:
                round_numbers = [0.1, 0.25, 0.5, 1.0]
            else:
                round_numbers = [0.01, 0.025, 0.05, 0.1]

            # Find nearby round numbers
            for round_num in round_numbers:
                # Find the nearest round numbers above and below current price
                lower_round = math.floor(current_price / round_num) * round_num
                upper_round = math.ceil(current_price / round_num) * round_num

                # Add levels if they're close enough (within 10%)
                for level_price in [lower_round, upper_round]:
                    if level_price > 0:
                        distance_pct = abs(current_price - level_price) / current_price * 100
                        if distance_pct <= 10.0:  # Only include levels within 10%
                            strength = LevelStrength.STRONG if distance_pct <= 2.0 else LevelStrength.MODERATE

                            levels.append(KeyLevel(
                                price=level_price,
                                level_type=LevelType.PSYCHOLOGICAL,
                                strength=strength,
                                description=f"Psychological Level ({level_price})",
                                distance_pct=distance_pct
                            ))

            # Remove duplicates
            unique_levels = []
            seen_prices = set()
            for level in levels:
                if level.price not in seen_prices:
                    unique_levels.append(level)
                    seen_prices.add(level.price)

            return unique_levels

        except Exception as e:
            logger.error(f"Error calculating psychological levels: {e}")
            return []

    def _calculate_dynamic_support_resistance(self, market_data: pd.DataFrame, current_price: float, lookback: int) -> List[KeyLevel]:
        """Calculate dynamic support and resistance based on price action"""
        try:
            if len(market_data) < lookback or 'high' not in market_data.columns:
                return []

            levels = []
            recent_data = market_data.tail(lookback)

            # Find swing highs and lows
            highs = recent_data['high'].values
            lows = recent_data['low'].values

            # Simple swing detection (can be enhanced with more sophisticated algorithms)
            swing_highs = []
            swing_lows = []

            window = 5  # Look for swings in 5-period windows

            for i in range(window, len(highs) - window):
                # Check if this is a swing high
                if highs[i] == max(highs[i-window:i+window+1]):
                    swing_highs.append(highs[i])

                # Check if this is a swing low
                if lows[i] == min(lows[i-window:i+window+1]):
                    swing_lows.append(lows[i])

            # Create levels from swing highs (resistance)
            for swing_high in swing_highs:
                distance_pct = abs(current_price - swing_high) / current_price * 100
                if distance_pct <= 15.0:  # Only include levels within 15%
                    levels.append(KeyLevel(
                        price=swing_high,
                        level_type=LevelType.DYNAMIC_SR,
                        strength=LevelStrength.MODERATE,
                        description="Dynamic Resistance (Swing High)",
                        distance_pct=distance_pct,
                        touches=1  # Could be enhanced to count actual touches
                    ))

            # Create levels from swing lows (support)
            for swing_low in swing_lows:
                distance_pct = abs(current_price - swing_low) / current_price * 100
                if distance_pct <= 15.0:  # Only include levels within 15%
                    levels.append(KeyLevel(
                        price=swing_low,
                        level_type=LevelType.DYNAMIC_SR,
                        strength=LevelStrength.MODERATE,
                        description="Dynamic Support (Swing Low)",
                        distance_pct=distance_pct,
                        touches=1  # Could be enhanced to count actual touches
                    ))

            return levels

        except Exception as e:
            logger.error(f"Error calculating dynamic support/resistance: {e}")
            return []

    def _calculate_previous_levels(self, market_data: pd.DataFrame, current_price: float) -> List[KeyLevel]:
        """Calculate previous day/week/month highs and lows"""
        try:
            if len(market_data) < 2:
                return []

            levels = []

            # Previous day high/low
            if len(market_data) >= 1:
                prev_high = market_data['high'].iloc[-1] if 'high' in market_data.columns else current_price * 1.01
                prev_low = market_data['low'].iloc[-1] if 'low' in market_data.columns else current_price * 0.99

                levels.extend([
                    KeyLevel(
                        price=prev_high,
                        level_type=LevelType.PREVIOUS_LEVELS,
                        strength=LevelStrength.MODERATE,
                        description="Previous Day High",
                        distance_pct=abs(current_price - prev_high) / current_price * 100
                    ),
                    KeyLevel(
                        price=prev_low,
                        level_type=LevelType.PREVIOUS_LEVELS,
                        strength=LevelStrength.MODERATE,
                        description="Previous Day Low",
                        distance_pct=abs(current_price - prev_low) / current_price * 100
                    )
                ])

            # Weekly high/low (if we have enough data)
            if len(market_data) >= 7:
                week_data = market_data.tail(7)
                week_high = week_data['high'].max() if 'high' in week_data.columns else current_price * 1.05
                week_low = week_data['low'].min() if 'low' in week_data.columns else current_price * 0.95

                levels.extend([
                    KeyLevel(
                        price=week_high,
                        level_type=LevelType.PREVIOUS_LEVELS,
                        strength=LevelStrength.STRONG,
                        description="Weekly High",
                        distance_pct=abs(current_price - week_high) / current_price * 100
                    ),
                    KeyLevel(
                        price=week_low,
                        level_type=LevelType.PREVIOUS_LEVELS,
                        strength=LevelStrength.STRONG,
                        description="Weekly Low",
                        distance_pct=abs(current_price - week_low) / current_price * 100
                    )
                ])

            return levels

        except Exception as e:
            logger.error(f"Error calculating previous levels: {e}")
            return []

    def _create_fallback_support(self, current_price: float) -> KeyLevel:
        """Create fallback support level"""
        return KeyLevel(
            price=current_price * 0.995,
            level_type=LevelType.PSYCHOLOGICAL,
            strength=LevelStrength.WEAK,
            description="Fallback Support (0.5% below)",
            distance_pct=0.5
        )

    def _create_fallback_resistance(self, current_price: float) -> KeyLevel:
        """Create fallback resistance level"""
        return KeyLevel(
            price=current_price * 1.005,
            level_type=LevelType.PSYCHOLOGICAL,
            strength=LevelStrength.WEAK,
            description="Fallback Resistance (0.5% above)",
            distance_pct=0.5
        )

    def _analyze_price_position(self, current_price: float, nearest_support: KeyLevel, nearest_resistance: KeyLevel) -> str:
        """Analyze current price position relative to key levels"""
        try:
            support_distance = abs(current_price - nearest_support.price) / current_price * 100
            resistance_distance = abs(current_price - nearest_resistance.price) / current_price * 100

            # Define thresholds for "near" levels
            near_threshold = 0.5  # 0.5%

            if support_distance <= near_threshold:
                return "AT_SUPPORT"
            elif resistance_distance <= near_threshold:
                return "AT_RESISTANCE"
            elif current_price > nearest_resistance.price:
                return "BREAKOUT_ABOVE"
            elif current_price < nearest_support.price:
                return "BREAKDOWN_BELOW"
            else:
                return "BETWEEN_LEVELS"

        except Exception as e:
            logger.error(f"Error analyzing price position: {e}")
            return "NEUTRAL"

    def _find_confluence_zones(self, all_levels: List[KeyLevel], current_price: float) -> List[Dict[str, Any]]:
        """Find areas where multiple levels converge"""
        try:
            confluence_zones = []
            confluence_threshold = 0.2  # 0.2% price range for confluence

            # Group levels by price proximity
            level_groups = []
            for level in all_levels:
                added_to_group = False
                for group in level_groups:
                    # Check if this level is close to any level in the group
                    for group_level in group:
                        price_diff_pct = abs(level.price - group_level.price) / current_price * 100
                        if price_diff_pct <= confluence_threshold:
                            group.append(level)
                            added_to_group = True
                            break
                    if added_to_group:
                        break

                if not added_to_group:
                    level_groups.append([level])

            # Find groups with multiple levels (confluence zones)
            for group in level_groups:
                if len(group) >= 2:  # At least 2 levels for confluence
                    avg_price = sum(level.price for level in group) / len(group)
                    level_types = list(set(level.level_type.value for level in group))
                    max_strength = max(level.strength.value for level in group)

                    confluence_zones.append({
                        'price': avg_price,
                        'level_count': len(group),
                        'level_types': level_types,
                        'strength': max_strength,
                        'distance_pct': abs(current_price - avg_price) / current_price * 100,
                        'description': f"Confluence Zone ({len(group)} levels)"
                    })

            # Sort by strength and proximity
            confluence_zones.sort(key=lambda x: (x['strength'], -x['distance_pct']), reverse=True)

            return confluence_zones[:5]  # Return top 5 confluence zones

        except Exception as e:
            logger.error(f"Error finding confluence zones: {e}")
            return []

    def _calculate_level_density(self, all_levels: List[KeyLevel], current_price: float) -> float:
        """Calculate the density of levels around current price"""
        try:
            # Count levels within 5% of current price
            nearby_levels = [
                level for level in all_levels
                if abs(level.price - current_price) / current_price * 100 <= 5.0
            ]

            # Density = number of levels per 1% price range
            density = len(nearby_levels) / 10.0  # 5% range = 10 half-percent intervals

            return min(density, 10.0)  # Cap at 10 for normalization

        except Exception as e:
            logger.error(f"Error calculating level density: {e}")
            return 1.0

    def _calculate_level_strength(self, levels: List[KeyLevel]) -> float:
        """Calculate overall strength score for a group of levels"""
        try:
            if not levels:
                return 0.0

            total_strength = 0.0
            total_weight = 0.0

            for level in levels:
                # Weight by strength and inverse distance
                strength_score = level.strength.value * 25  # Convert to 0-100 scale
                distance_weight = 1.0 / (1.0 + level.distance_pct)  # Closer levels have more weight

                total_strength += strength_score * distance_weight
                total_weight += distance_weight

            return min(total_strength / total_weight if total_weight > 0 else 0.0, 100.0)

        except Exception as e:
            logger.error(f"Error calculating level strength: {e}")
            return 50.0

    def _calculate_breakout_probability(self, current_price: float, nearest_support: KeyLevel,
                                     nearest_resistance: KeyLevel, market_data: pd.DataFrame) -> float:
        """Calculate probability of breakout based on market conditions"""
        try:
            breakout_probability = 50.0  # Base probability

            # Factor 1: Distance from levels
            support_distance = abs(current_price - nearest_support.price) / current_price * 100
            resistance_distance = abs(current_price - nearest_resistance.price) / current_price * 100

            # Closer to levels = lower breakout probability
            if support_distance < 1.0 or resistance_distance < 1.0:
                breakout_probability -= 20.0

            # Factor 2: Level strength
            avg_strength = (nearest_support.strength.value + nearest_resistance.strength.value) / 2
            breakout_probability -= avg_strength * 5  # Stronger levels = lower breakout probability

            # Factor 3: Market volatility
            if len(market_data) >= 20 and 'close' in market_data.columns:
                recent_volatility = market_data['close'].tail(20).pct_change().std() * 100
                if recent_volatility > 2.0:  # High volatility
                    breakout_probability += 15.0
                elif recent_volatility < 0.5:  # Low volatility
                    breakout_probability -= 10.0

            # Factor 4: Volume (if available)
            if len(market_data) >= 5 and 'volume' in market_data.columns:
                recent_volume = market_data['volume'].tail(5).mean()
                avg_volume = market_data['volume'].mean()
                volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1.0

                if volume_ratio > 1.5:  # High volume
                    breakout_probability += 10.0
                elif volume_ratio < 0.7:  # Low volume
                    breakout_probability -= 10.0

            return max(0.0, min(100.0, breakout_probability))

        except Exception as e:
            logger.error(f"Error calculating breakout probability: {e}")
            return 50.0

    def _determine_recommended_action(self, current_price: float, nearest_support: KeyLevel,
                                    nearest_resistance: KeyLevel, support_strength: float,
                                    resistance_strength: float, breakout_probability: float) -> str:
        """Determine recommended trading action based on key levels analysis"""
        try:
            support_distance = abs(current_price - nearest_support.price) / current_price * 100
            resistance_distance = abs(current_price - nearest_resistance.price) / current_price * 100

            # Near support with strong support
            if support_distance <= 1.0 and support_strength >= 70.0:
                return "BUY_SUPPORT"

            # Near resistance with strong resistance
            elif resistance_distance <= 1.0 and resistance_strength >= 70.0:
                return "SELL_RESISTANCE"

            # High breakout probability
            elif breakout_probability >= 75.0:
                return "WAIT_BREAKOUT"

            # Between levels with no clear bias
            else:
                return "NEUTRAL"

        except Exception as e:
            logger.error(f"Error determining recommended action: {e}")
            return "NEUTRAL"

    def _create_fallback_result(self, current_price: float, symbol: str) -> KeyLevelsResult:
        """Create fallback result when calculation fails"""
        timestamp = pd.Timestamp.now().isoformat()

        fallback_support = self._create_fallback_support(current_price)
        fallback_resistance = self._create_fallback_resistance(current_price)

        return KeyLevelsResult(
            current_price=current_price,
            symbol=symbol,
            timestamp=timestamp,
            support_levels=[fallback_support],
            resistance_levels=[fallback_resistance],
            nearest_support=fallback_support,
            nearest_resistance=fallback_resistance,
            price_position="BETWEEN_LEVELS",
            level_density=1.0,
            confluence_zones=[],
            support_strength=25.0,
            resistance_strength=25.0,
            breakout_probability=50.0,
            recommended_action="NEUTRAL"
        )
