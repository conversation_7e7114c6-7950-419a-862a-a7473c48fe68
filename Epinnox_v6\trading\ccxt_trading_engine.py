"""
CCXT Trading Engine for Epinnox v6
Real exchange API integration with comprehensive error handling and safety measures
"""

import ccxt
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
import traceback

# Set up logger
logger = logging.getLogger(__name__)


class CCXTTradingEngine(QObject):
    """
    Real trading engine using CCXT for exchange integration
    Handles order placement, position tracking, and risk management
    """
    
    # Signals for UI updates
    order_placed = pyqtSignal(dict)  # order_info
    order_filled = pyqtSignal(dict)  # order_info
    order_cancelled = pyqtSignal(dict)  # order_info
    position_updated = pyqtSignal(dict)  # position_info
    balance_updated = pyqtSignal(dict)  # balance_info
    error_occurred = pyqtSignal(str)  # error_message
    status_updated = pyqtSignal(str)  # status_message
    trade_executed = pyqtSignal(dict)  # Signal for executed trades
    
    def __init__(self, exchange_name="htx", demo_mode=True):
        super().__init__()
        
        self.exchange_name = exchange_name
        self.demo_mode = demo_mode
        self.exchange = None
        self.is_connected = False
        
        # Trading state
        self.open_orders = {}
        self.open_positions = {}
        self.account_balance = {}
        self.trading_fees = {}
        
        # Risk management settings
        self.max_position_size = 1000.0  # Maximum position size in USD
        self.max_daily_loss = 100.0      # Maximum daily loss in USD
        self.min_balance_threshold = 3.0  # Minimum balance to maintain (small account mode)
        self.daily_pnl = 0.0
        self.daily_reset_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Update timers
        self.position_timer = QTimer()
        self.position_timer.timeout.connect(self.update_positions)
        self.position_timer.start(5000)  # Update every 5 seconds
        
        self.balance_timer = QTimer()
        self.balance_timer.timeout.connect(self.update_balance)
        self.balance_timer.start(10000)  # Update every 10 seconds
        
        # Initialize exchange
        self.initialize_exchange()
    
    def initialize_exchange(self):
        """Initialize CCXT exchange connection"""
        try:
            if self.demo_mode:
                self.status_updated.emit("Initializing in DEMO mode")
                # Create exchange instance without credentials for demo
                if self.exchange_name == "htx":
                    self.exchange = ccxt.htx({
                        'enableRateLimit': True,
                        'sandbox': False,  # HTX doesn't have sandbox
                    })
                else:
                    self.exchange = getattr(ccxt, self.exchange_name)({
                        'enableRateLimit': True,
                        'sandbox': True,
                    })
                
                self.is_connected = True
                self.status_updated.emit("DEMO mode initialized")
                return True
            
            else:
                # Load real API credentials
                print(f"🔑 CCXTTradingEngine: Loading credentials for {self.exchange_name}")
                credentials = self.load_api_credentials()
                if not credentials:
                    self.error_occurred.emit("No API credentials found. Switching to DEMO mode.")
                    print("❌ CCXTTradingEngine: No credentials found, switching to DEMO mode")
                    self.demo_mode = True
                    return self.initialize_exchange()

                print(f"✅ CCXTTradingEngine: Credentials loaded successfully")
                print(f"   API Key: {credentials['api_key'][:8]}...{credentials['api_key'][-4:]}")
                print(f"   Sandbox: {'ON' if credentials.get('sandbox', False) else 'OFF'}")
                
                # Initialize with real credentials
                if self.exchange_name == "htx":
                    self.exchange = ccxt.htx({
                        'apiKey': credentials['api_key'],
                        'secret': credentials['secret'],
                        'password': credentials.get('passphrase', ''),
                        'enableRateLimit': True,
                        'sandbox': credentials.get('sandbox', False),
                        'timeout': 30000,
                        'rateLimit': 100,
                        'options': {
                            'defaultType': 'swap',  # HTX Linear Swaps
                            'marginMode': 'cross',  # Cross margin mode
                            'fetchCurrencies': False,  # Disable problematic endpoint
                            'hedgeMode': True,  # Enable hedge mode for HTX
                            'marginBuffer': 1.2,  # 20% margin buffer for safety
                        },
                        'urls': {
                            'api': {
                                'swap': 'https://api.hbdm.com',  # HTX Linear Swap API
                            }
                        }
                    })
                    print(f"🔧 HTX configured for LINEAR SWAP trading")
                    print(f"   Default Type: swap")
                    print(f"   Margin Mode: cross")
                    print(f"   API Endpoint: https://api.hbdm.com")
                else:
                    # For other exchanges
                    exchange_class = getattr(ccxt, self.exchange_name)
                    self.exchange = exchange_class({
                        'apiKey': credentials['api_key'],
                        'secret': credentials['secret'],
                        'password': credentials.get('passphrase', ''),
                        'enableRateLimit': True,
                        'sandbox': credentials.get('sandbox', False),
                        'timeout': 30000,
                        'rateLimit': 100,
                    })
                
                # Test connection
                self.exchange.load_markets()
                self.is_connected = True
                self.status_updated.emit("Connected to live exchange")
                return True
                
        except Exception as e:
            self.error_occurred.emit(f"Exchange initialization failed: {str(e)}")
            self.demo_mode = True
            self.is_connected = False
            return False
    
    def load_api_credentials(self) -> Optional[Dict]:
        """Load API credentials from config file"""
        try:
            # Use the same credential loading system as the main application
            from config.production_loader import get_live_trading_credentials

            creds = get_live_trading_credentials()
            if creds and creds.get('apiKey') and creds.get('secret'):
                # Convert to the format expected by this class
                return {
                    'api_key': creds['apiKey'],
                    'secret': creds['secret'],
                    'passphrase': creds.get('password', ''),
                    'sandbox': creds.get('sandbox', False)
                }

            # Fallback: Try to load from legacy config file
            import os
            config_path = os.path.join("config", "api_credentials.json")

            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    credentials = json.load(f)
                    return credentials.get(self.exchange_name, {})

            # Fallback: Try environment variables
            api_key = os.getenv(f"{self.exchange_name.upper()}_API_KEY")
            secret = os.getenv(f"{self.exchange_name.upper()}_SECRET")

            if api_key and secret:
                return {
                    'api_key': api_key,
                    'secret': secret,
                    'passphrase': os.getenv(f"{self.exchange_name.upper()}_PASSPHRASE", '')
                }

            return None
            
        except Exception as e:
            self.error_occurred.emit(f"Error loading credentials: {str(e)}")
            return None
    
    def get_existing_position_leverage(self, symbol):
        """Get leverage from existing position for the symbol to avoid mismatch errors"""
        try:
            if self.demo_mode:
                return 20  # Default for demo mode

            # Fetch current positions to get existing leverage
            positions = self.exchange.fetch_positions([symbol])
            for pos in positions:
                if pos['symbol'] == symbol and (pos.get('contracts', 0) > 0 or pos.get('size', 0) > 0):
                    leverage = pos.get('leverage', pos.get('marginRatio', 20))
                    print(f"🔍 Found existing position leverage for {symbol}: {leverage}x")
                    return int(leverage)  # Ensure integer for HTX

            # No existing position, check if DOGE/USDT:USDT has existing position
            if symbol == 'DOGE/USDT:USDT':
                # We know from testing that DOGE position uses 75x leverage
                print(f"🔍 Using known DOGE position leverage: 75x")
                return 75

            # No existing position, use default
            print(f"🔍 No existing position for {symbol}, using default leverage: 20x")
            return 20

        except Exception as e:
            print(f"⚠️ Error fetching position leverage: {e}")
            # For DOGE, use the known leverage from our test
            if symbol == 'DOGE/USDT:USDT':
                print(f"🔍 Fallback: Using known DOGE leverage: 75x")
                return 75
            return 20  # Fallback to default

    def place_limit_order(self, symbol: str, side: str, amount: float, price: float, params: Dict = None) -> Optional[Dict]:
        """Place a limit order with automatic minimum size adjustment"""
        try:
            if params is None:
                params = {}

            # 🚨 HTX HEDGE MODE FIX: Add position side for hedge mode
            if self.exchange_name == "htx":
                # HTX requires position side in hedge mode
                if side.lower() == 'buy':
                    params['positionSide'] = 'long'
                else:
                    params['positionSide'] = 'short'

                # 🚨 CRITICAL FIX: Use correct leverage for the symbol
                correct_leverage = self.get_existing_position_leverage(symbol)

                # Ensure other HTX-specific parameters
                params.setdefault('offset', 'open')
                params['lever_rate'] = correct_leverage  # Use correct leverage
                params.setdefault('marginMode', 'cross')

                print(f"🔧 HTX HEDGE MODE: {side.upper()} order with positionSide={params['positionSide']}, leverage={correct_leverage}x")

            # Validate minimum order size
            adjusted_amount = self.auto_adjust_minimum_order_size(symbol, amount)
            if adjusted_amount != amount:
                print(f"AUTO-ADJUSTED: Order size from {amount:.8f} to {adjusted_amount:.8f} for {symbol}")
                amount = adjusted_amount

            # Validate inputs
            if not self.validate_order_inputs(symbol, side, amount, price):
                print("Order validation failed")
                return None

            # Check risk limits
            if not self.check_risk_limits(symbol, side, amount, price):
                return None

            # Place order
            if self.demo_mode:
                # Simulate order in demo mode
                order = self.simulate_limit_order(symbol, side, amount, price, params)
            else:
                # Place real order
                order = self.exchange.create_limit_order(symbol, side, amount, price, params)

            # Store order
            self.open_orders[order['id']] = order
            print(f"Order stored: {order['id']}")
            return order

        except Exception as e:
            error_msg = f"Error placing limit order: {str(e)}"
            self.error_occurred.emit(error_msg)
            print(error_msg)
            return None
    
    def place_market_order(self, symbol: str, side: str, amount: float, params: Dict = None) -> Optional[Dict]:
        """
        DEPRECATED: Market orders are not allowed for autonomous trading
        This method is disabled for safety - use place_limit_order instead
        """
        error_msg = "[AUTONOMOUS TRADING VIOLATION] Market orders are prohibited. Use LIMIT orders only."
        logger.error(error_msg)
        print(f"[ERROR] {error_msg}")
        self.error_occurred.emit(error_msg)
        return None

    
    def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an open order"""
        try:
            if self.demo_mode:
                # Simulate cancellation
                if order_id in self.open_orders:
                    order = self.open_orders[order_id]
                    order['status'] = 'canceled'
                    del self.open_orders[order_id]
                    self.order_cancelled.emit(order)
                    return True
                return False
            else:
                # Cancel real order
                result = self.exchange.cancel_order(order_id, symbol)
                
                if order_id in self.open_orders:
                    order = self.open_orders[order_id]
                    order['status'] = 'canceled'
                    del self.open_orders[order_id]
                    self.order_cancelled.emit(order)
                
                return True
                
        except Exception as e:
            self.error_occurred.emit(f"Error cancelling order: {str(e)}")
            return False
    
    def cancel_all_orders(self, symbol: str = None) -> int:
        """Cancel all open orders for a symbol or all symbols"""
        try:
            cancelled_count = 0
            
            if self.demo_mode:
                # Cancel all demo orders
                orders_to_cancel = list(self.open_orders.keys())
                for order_id in orders_to_cancel:
                    order = self.open_orders[order_id]
                    if symbol is None or order['symbol'] == symbol:
                        if self.cancel_order(order_id, order['symbol']):
                            cancelled_count += 1
            else:
                # Cancel all real orders
                if symbol:
                    orders = self.exchange.fetch_open_orders(symbol)
                else:
                    orders = self.exchange.fetch_open_orders()
                
                for order in orders:
                    if self.cancel_order(order['id'], order['symbol']):
                        cancelled_count += 1
            
            self.status_updated.emit(f"Cancelled {cancelled_count} orders")
            return cancelled_count
            
        except Exception as e:
            self.error_occurred.emit(f"Error cancelling orders: {str(e)}")
            return 0
    
    def close_position(self, symbol: str, side: str = None) -> bool:
        """Close a position using market order"""
        try:
            # Get current position
            position = self.get_position(symbol)
            if not position or position['size'] == 0:
                self.status_updated.emit(f"No open position for {symbol}")
                return True
            
            # Determine close side
            position_side = 'long' if position['side'] == 'long' else 'short'
            close_side = 'sell' if position_side == 'long' else 'buy'
            
            # Close with market order
            close_params = {'reduceOnly': True}
            order = self.place_market_order(symbol, close_side, abs(position['size']), close_params)
            
            if order:
                self.status_updated.emit(f"Closed {position_side} position for {symbol}")
                return True
            
            return False
            
        except Exception as e:
            self.error_occurred.emit(f"Error closing position: {str(e)}")
            return False
    
    def close_all_positions(self) -> int:
        """Close all open positions"""
        try:
            closed_count = 0
            positions = self.get_all_positions()
            
            for symbol, position in positions.items():
                if position['size'] != 0:
                    if self.close_position(symbol):
                        closed_count += 1
            
            self.status_updated.emit(f"Closed {closed_count} positions")
            return closed_count
            
        except Exception as e:
            self.error_occurred.emit(f"Error closing positions: {str(e)}")
            return 0
    
    def set_leverage(self, symbol: str, leverage: int) -> bool:
        """Set leverage for HTX Linear Swaps"""
        try:
            if self.demo_mode:
                self.status_updated.emit(f"DEMO: Set leverage {leverage}x for {symbol}")
                return True

            # Set real leverage using the working method from me2_stable.py
            if self.exchange_name == "htx":
                print(f"🔧 Setting HTX Linear Swap leverage: {leverage}x for {symbol}")
                # Use the same method as me2_stable.py with marginMode parameter
                result = self.exchange.set_leverage(leverage, symbol, params={'marginMode': 'cross'})
                print(f"✅ HTX leverage set successfully: {leverage}x")
            else:
                result = self.exchange.set_leverage(leverage, symbol)

            success_msg = f"Set leverage {leverage}x for {symbol}"
            print(f"✅ {success_msg}")
            self.status_updated.emit(success_msg)
            return True
            
        except Exception as e:
            self.error_occurred.emit(f"Error setting leverage: {str(e)}")
            return False

    # Data fetching methods
    def get_ticker(self, symbol: str) -> Optional[Dict]:
        """Get current ticker data"""
        try:
            if self.demo_mode:
                # Return mock ticker data
                return {
                    'symbol': symbol,
                    'last': 0.35,  # Mock DOGE price
                    'bid': 0.349,
                    'ask': 0.351,
                    'high': 0.36,
                    'low': 0.34,
                    'volume': 1000000
                }

            return self.exchange.fetch_ticker(symbol)

        except Exception as e:
            self.error_occurred.emit(f"Error fetching ticker: {str(e)}")
            return None

    def get_orderbook(self, symbol: str) -> Optional[Dict]:
        """Get current order book"""
        try:
            if self.demo_mode:
                # Return mock order book
                return {
                    'symbol': symbol,
                    'bids': [[0.349, 1000], [0.348, 2000], [0.347, 1500]],
                    'asks': [[0.351, 1000], [0.352, 2000], [0.353, 1500]],
                    'timestamp': int(time.time() * 1000)
                }

            return self.exchange.fetch_order_book(symbol)

        except Exception as e:
            self.error_occurred.emit(f"Error fetching order book: {str(e)}")
            return None

    def get_position(self, symbol: str) -> Optional[Dict]:
        """Get current position for symbol"""
        try:
            if self.demo_mode:
                # Return mock position
                return self.open_positions.get(symbol, {
                    'symbol': symbol,
                    'size': 0,
                    'side': None,
                    'unrealizedPnl': 0,
                    'percentage': 0,
                    'entryPrice': 0,
                    'markPrice': 0
                })

            positions = self.exchange.fetch_positions([symbol])
            return positions[0] if positions else None

        except Exception as e:
            self.error_occurred.emit(f"Error fetching position: {str(e)}")
            return None

    def get_all_positions(self) -> Dict:
        """Get all open positions for HTX Linear Swaps with enhanced error handling"""
        try:
            if self.demo_mode:
                return self.open_positions

            # For HTX Linear Swaps, fetch positions with specific parameters
            if self.exchange_name == "htx":
                try:
                    # Try different HTX position endpoints
                    positions = self.exchange.fetch_positions(None, {'type': 'swap'})
                except Exception as e:
                    print(f"⚠️ HTX swap positions failed: {e}")
                    try:
                        # Fallback to cross margin positions
                        positions = self.exchange.fetch_positions(None, {'marginMode': 'cross'})
                    except Exception as e2:
                        print(f"⚠️ HTX cross positions failed: {e2}")
                        # Final fallback to basic positions
                        positions = self.exchange.fetch_positions()
            else:
                positions = self.exchange.fetch_positions()

            # Filter positions with non-zero size, handling different field names
            filtered_positions = {}
            for pos in positions:
                try:
                    # HTX Linear Swaps use different field names - check all possible fields
                    size = pos.get('size', 0)
                    if size == 0:
                        size = pos.get('contracts', 0)
                    if size == 0:
                        size = pos.get('amount', 0)
                    if size == 0:
                        size = pos.get('contractSize', 0)
                    if size == 0:
                        size = pos.get('notional', 0)

                    if size != 0:
                        filtered_positions[pos['symbol']] = pos

                except (KeyError, TypeError) as e:
                    print(f"⚠️ Position data format issue: {e}, position: {pos}")
                    continue

            return filtered_positions

        except Exception as e:
            error_msg = f"Error fetching positions: {str(e)}"
            print(f"❌ {error_msg}")  # Print to terminal
            self.error_occurred.emit(error_msg)
            return {}

    def get_balance(self) -> Optional[Dict]:
        """Get account balance for HTX Linear Swaps"""
        try:
            if self.demo_mode:
                # Return mock balance
                return {
                    'USDT': {'free': 1000.0, 'used': 0.0, 'total': 1000.0},
                    'total': {'USDT': 1000.0}
                }

            # For HTX Linear Swaps, we need to fetch swap balance specifically
            if self.exchange_name == "htx":
                # HTX Linear Swap balance requires specific parameters
                balance = self.exchange.fetch_balance({'type': 'swap'})
                return balance
            else:
                return self.exchange.fetch_balance()

        except Exception as e:
            error_msg = f"Error fetching balance: {str(e)}"
            print(f"❌ {error_msg}")  # Print to terminal
            self.error_occurred.emit(error_msg)
            return None

    def get_order_status(self, order_id: str, symbol: str) -> Optional[Dict]:
        """Get order status using CCXT fetch_order method"""
        try:
            if self.demo_mode:
                # Return mock order status from stored orders
                if order_id in self.open_orders:
                    return self.open_orders[order_id]
                else:
                    # Return a mock filled order
                    return {
                        'id': order_id,
                        'symbol': symbol,
                        'status': 'closed',
                        'filled': 100.0,
                        'remaining': 0.0,
                        'type': 'limit',
                        'side': 'buy',
                        'amount': 100.0,
                        'price': 0.35,
                        'timestamp': int(time.time() * 1000)
                    }

            # Fetch real order status from exchange
            order = self.exchange.fetch_order(order_id, symbol)
            return order

        except Exception as e:
            error_msg = f"Error fetching order status: {str(e)}"
            print(f"❌ {error_msg}")  # Print to terminal
            self.error_occurred.emit(error_msg)
            return None

    def fetch_order(self, order_id: str, symbol: str) -> Optional[Dict]:
        """Alias for get_order_status to match CCXT naming convention"""
        return self.get_order_status(order_id, symbol)

    def auto_adjust_minimum_order_size(self, symbol: str, amount: float) -> float:
        """🚨 AUTO-ADJUST: Automatically adjust order size to meet minimum requirements"""
        try:
            # Check if exchange and markets data are available
            if not self.exchange or not hasattr(self.exchange, 'markets'):
                print(f"⚠️ Exchange markets data not available for auto-adjustment")
                return amount

            # Get market info for the symbol
            if symbol not in self.exchange.markets:
                print(f"⚠️ Symbol {symbol} not found in exchange markets")
                return amount

            market = self.exchange.markets[symbol]
            limits = market.get('limits', {})
            amount_limits = limits.get('amount', {})
            min_amount = amount_limits.get('min')

            # 🚨 HTX CONTRACT FIX: Handle minimum comparison correctly for contracts
            if 'DOGE' in symbol and self.exchange_name == "htx":
                # For HTX DOGE: amount is in contracts, min_amount is in DOGE
                # Convert min_amount to contracts for proper comparison
                if min_amount is not None:
                    min_contracts = max(1.0, min_amount / 100.0)  # Convert DOGE to contracts
                    if amount < min_contracts:
                        print(f"🔧 HTX AUTO-ADJUSTING: {amount:.8f} contracts below minimum {min_contracts:.8f} contracts")
                        adjusted_amount = min_contracts
                        print(f"🔧 HTX AUTO-ADJUSTING: Setting to minimum: {adjusted_amount:.8f} contracts")
                        return adjusted_amount
                    else:
                        print(f"✅ HTX Order amount {amount:.8f} contracts meets minimum {min_contracts:.8f} contracts for {symbol}")
                else:
                    # No minimum defined, ensure at least 1 contract
                    if amount < 1.0:
                        print(f"🔧 HTX AUTO-ADJUSTING: Ensuring minimum 1 contract, got {amount:.8f}")
                        return 1.0
                    else:
                        print(f"✅ HTX Order amount {amount:.8f} contracts is sufficient for {symbol}")
            else:
                # Standard validation for non-HTX or non-DOGE
                if min_amount is not None and amount < min_amount:
                    print(f"🔧 AUTO-ADJUSTING: Order amount {amount:.8f} below minimum {min_amount:.8f} for {symbol}")
                    adjusted_amount = min_amount
                    print(f"🔧 AUTO-ADJUSTING: Setting order amount to: {adjusted_amount:.8f}")
                    return adjusted_amount

            return amount

        except Exception as e:
            print(f"⚠️ Error in auto-adjustment: {e}")
            return amount

    # Validation and risk management
    def validate_order_inputs(self, symbol: str, side: str, amount: float, price: float) -> bool:
        """Validate order inputs"""
        try:
            # Basic validation
            if not symbol or not side:
                self.error_occurred.emit("Invalid symbol or side")
                return False

            if amount <= 0:
                self.error_occurred.emit("Amount must be positive")
                return False

            if price <= 0:
                self.error_occurred.emit("Price must be positive")
                return False

            if side not in ['buy', 'sell']:
                self.error_occurred.emit("Side must be 'buy' or 'sell'")
                return False

            # Check minimum order size
            if amount < 1.0:  # Minimum $1 order
                self.error_occurred.emit("Order size too small (minimum $1)")
                return False

            return True

        except Exception as e:
            self.error_occurred.emit(f"Validation error: {str(e)}")
            return False

    def check_risk_limits(self, symbol: str, side: str, amount: float, price: float) -> bool:
        """Check risk management limits with enhanced portfolio exposure monitoring"""
        try:
            # 🚨 HTX CONTRACT FIX: Calculate order value correctly for contracts
            if 'DOGE' in symbol and self.exchange_name == "htx":
                # For HTX DOGE: amount is in contracts, convert to actual DOGE for value calculation
                actual_doge = amount * 100.0  # 1 contract = 100 DOGE
                order_value = actual_doge * price
                print(f"🔧 HTX RISK CHECK: {amount:.6f} contracts = {actual_doge:.0f} DOGE = ${order_value:.2f}")
            else:
                # For other symbols: amount is actual quantity
                order_value = amount * price

            # Check maximum position size
            if order_value > self.max_position_size:
                self.error_occurred.emit(f"Order exceeds max position size (${self.max_position_size})")
                return False

            # Check daily loss limit
            if self.daily_pnl < -self.max_daily_loss:
                self.error_occurred.emit(f"Daily loss limit reached (${self.max_daily_loss})")
                return False

            # Check minimum balance
            balance = self.get_balance()
            if balance:
                usdt_balance = balance.get('USDT', {}).get('free', 0)
                if usdt_balance < self.min_balance_threshold:
                    self.error_occurred.emit(f"Insufficient balance (minimum ${self.min_balance_threshold})")
                    return False

                # CRITICAL: Check portfolio exposure limits
                current_positions = self.get_all_positions()
                total_exposure = 0
                for pos in current_positions.values():
                    pos_value = abs(pos.get('notional', 0))
                    if pos_value == 0:
                        # Calculate from size and price if notional not available
                        size = pos.get('size', pos.get('contracts', 0))
                        price_val = pos.get('markPrice', pos.get('price', price))
                        pos_value = abs(size * price_val)
                    total_exposure += pos_value

                # Add new order to exposure calculation
                new_total_exposure = total_exposure + order_value
                exposure_pct = (new_total_exposure / usdt_balance) * 100 if usdt_balance > 0 else 0

                # Enforce 80% maximum portfolio exposure
                if exposure_pct > 80:
                    self.error_occurred.emit(f"Portfolio exposure would exceed 80% limit (current: {exposure_pct:.1f}%)")
                    print(f"🚨 PORTFOLIO RISK LIMIT: New exposure {exposure_pct:.1f}% > 80%")
                    return False

                # Check if we have enough balance for the order
                if order_value > usdt_balance * 0.95:  # Leave 5% buffer
                    self.error_occurred.emit("Insufficient balance for order")
                    return False

            return True

        except Exception as e:
            self.error_occurred.emit(f"Risk check error: {str(e)}")
            return False

    # Simulation methods for demo mode
    def simulate_limit_order(self, symbol: str, side: str, amount: float, price: float, params: Dict) -> Dict:
        """Simulate a limit order in demo mode"""
        order_id = f"demo_limit_{int(time.time() * 1000)}"
        order = {
            'id': order_id,
            'symbol': symbol,
            'type': 'limit',
            'side': side,
            'amount': amount,
            'price': price,
            'status': 'open',
            'filled': 0,
            'remaining': amount,
            'timestamp': int(time.time() * 1000),
            'params': params
        }

        self.open_orders[order_id] = order
        return order

    def simulate_market_order(self, symbol: str, side: str, amount: float, params: Dict) -> Dict:
        """Simulate a market order in demo mode"""
        # Get current price
        ticker = self.get_ticker(symbol)
        fill_price = ticker['last'] if ticker else 0.35

        order_id = f"demo_market_{int(time.time() * 1000)}"
        order = {
            'id': order_id,
            'symbol': symbol,
            'type': 'market',
            'side': side,
            'amount': amount,
            'price': fill_price,
            'status': 'closed',
            'filled': amount,
            'remaining': 0,
            'timestamp': int(time.time() * 1000),
            'params': params
        }

        # Update demo position
        self.update_demo_position(symbol, side, amount, fill_price)

        return order

    def update_demo_position(self, symbol: str, side: str, amount: float, price: float):
        """Update demo position after trade"""
        try:
            if symbol not in self.open_positions:
                self.open_positions[symbol] = {
                    'symbol': symbol,
                    'size': 0,
                    'side': None,
                    'unrealizedPnl': 0,
                    'percentage': 0,
                    'entryPrice': 0,
                    'markPrice': price
                }

            position = self.open_positions[symbol]

            if side == 'buy':
                if position['size'] <= 0:  # Opening long or reducing short
                    if position['size'] < 0:  # Reducing short
                        reduction = min(amount, abs(position['size']))
                        position['size'] += reduction
                        amount -= reduction

                    if amount > 0:  # Opening long
                        position['size'] += amount
                        position['side'] = 'long'
                        position['entryPrice'] = price

            else:  # sell
                if position['size'] >= 0:  # Opening short or reducing long
                    if position['size'] > 0:  # Reducing long
                        reduction = min(amount, position['size'])
                        position['size'] -= reduction
                        amount -= reduction

                    if amount > 0:  # Opening short
                        position['size'] -= amount
                        position['side'] = 'short'
                        position['entryPrice'] = price

            # Update mark price
            position['markPrice'] = price

            # Calculate unrealized PnL
            if position['size'] != 0:
                if position['side'] == 'long':
                    position['unrealizedPnl'] = position['size'] * (price - position['entryPrice'])
                else:
                    position['unrealizedPnl'] = abs(position['size']) * (position['entryPrice'] - price)
            else:
                position['unrealizedPnl'] = 0
                position['side'] = None

            self.position_updated.emit(position)

        except Exception as e:
            self.error_occurred.emit(f"Error updating demo position: {str(e)}")

    # Update methods
    def update_positions(self):
        """Update all positions (called by timer)"""
        try:
            if not self.is_connected:
                return

            positions = self.get_all_positions()
            for symbol, position in positions.items():
                self.position_updated.emit(position)

        except Exception as e:
            # Don't emit error for routine updates to avoid spam
            pass

    def update_balance(self):
        """Update account balance (called by timer)"""
        try:
            if not self.is_connected:
                return

            balance = self.get_balance()
            if balance:
                self.account_balance = balance
                self.balance_updated.emit(balance)

        except Exception as e:
            # Don't emit error for routine updates to avoid spam
            pass

    def reset_daily_pnl(self):
        """Reset daily PnL if new day"""
        try:
            now = datetime.now()
            if now.date() > self.daily_reset_time.date():
                self.daily_pnl = 0.0
                self.daily_reset_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
                self.status_updated.emit("Daily PnL reset")
        except Exception as e:
            self.error_occurred.emit(f"Error resetting daily PnL: {str(e)}")

    def extract_and_emit_trade_data(self, order: Dict, symbol: str, side: str, amount: float):
        """Extract trade execution data from HTX order response and emit trade signal"""
        try:
            # 🔧 ENHANCED: Ensure no None values are passed to position tracker

            # Validate input parameters first
            if not symbol or not side or amount is None or amount <= 0:
                print(f"❌ Invalid trade data: symbol={symbol}, side={side}, amount={amount}")
                return

            # Extract execution data from HTX order response
            execution_price = None
            execution_amount = amount  # Start with the provided amount
            execution_fee = 0.0

            # Try to get execution price from different fields
            if order and isinstance(order, dict):
                if 'price' in order and order['price'] is not None:
                    execution_price = float(order['price'])
                elif 'average' in order and order['average'] is not None:
                    execution_price = float(order['average'])
                elif 'info' in order and order['info']:
                    info = order['info']
                    # HTX specific fields
                    if 'trade_avg_price' in info and info['trade_avg_price'] is not None:
                        execution_price = float(info['trade_avg_price'])
                    elif 'price' in info and info['price'] is not None:
                        execution_price = float(info['price'])
                    elif 'avg_price' in info and info['avg_price'] is not None:
                        execution_price = float(info['avg_price'])

                # Get actual filled amount (prefer filled amount over input amount)
                if 'filled' in order and order['filled'] is not None and order['filled'] > 0:
                    execution_amount = float(order['filled'])
                elif 'info' in order and order['info']:
                    info = order['info']
                    if 'trade_volume' in info and info['trade_volume'] is not None:
                        execution_amount = float(info['trade_volume'])
                    elif 'volume' in info and info['volume'] is not None:
                        execution_amount = float(info['volume'])

                # Get fee information
                if 'fee' in order and order['fee']:
                    fee_info = order['fee']
                    if 'cost' in fee_info and fee_info['cost'] is not None:
                        execution_fee = float(fee_info['cost'])
                elif 'info' in order and order['info']:
                    info = order['info']
                    if 'fee' in info and info['fee'] is not None:
                        execution_fee = float(info['fee'])
                    elif 'trade_fee' in info and info['trade_fee'] is not None:
                        execution_fee = float(info['trade_fee'])

            # 🔧 CRITICAL: Ensure execution_amount is never None or zero
            if execution_amount is None or execution_amount <= 0:
                execution_amount = amount  # Fallback to original amount
                print(f"⚠️ Invalid execution amount, using original: {execution_amount}")

            # Fallback to current market price if no execution price found
            if execution_price is None or execution_price <= 0:
                ticker = self.get_ticker(symbol)
                execution_price = ticker['last'] if ticker and ticker.get('last') else 1.0
                print(f"⚠️ No execution price found, using market price: {execution_price}")

            # 🔧 CRITICAL: Final validation before creating trade data
            if execution_amount is None or execution_amount <= 0:
                print(f"❌ Cannot create trade with invalid amount: {execution_amount}")
                return

            if execution_price is None or execution_price <= 0:
                print(f"❌ Cannot create trade with invalid price: {execution_price}")
                return

            # Create trade data with guaranteed non-None values
            trade_data = {
                'symbol': symbol,
                'side': side,
                'amount': float(execution_amount),  # Ensure it's a float
                'price': float(execution_price),   # Ensure it's a float
                'fee': float(execution_fee),       # Ensure it's a float
                'timestamp': int(time.time() * 1000),
                'order_id': order.get('id', '') if order else '',
                'type': order.get('type', 'market') if order else 'market'
            }

            print(f"📊 Trade executed: {side.upper()} {execution_amount} {symbol} @ {execution_price}")

            # 🔧 ENHANCED: Log trade data for debugging
            print(f"🔍 Trade data: {trade_data}")

            # Emit trade signal
            self.trade_executed.emit(trade_data)

        except Exception as e:
            print(f"⚠️ Error extracting trade data: {e}")
            import traceback
            traceback.print_exc()

            # 🔧 ENHANCED: Improved fallback with validation
            try:
                # Ensure fallback values are valid
                fallback_amount = amount if amount and amount > 0 else 1.0
                fallback_price = 1.0

                # Try to get a reasonable price
                try:
                    ticker = self.get_ticker(symbol)
                    if ticker and ticker.get('last'):
                        fallback_price = float(ticker['last'])
                except:
                    pass

                fallback_trade = {
                    'symbol': symbol,
                    'side': side,
                    'amount': float(fallback_amount),
                    'price': float(fallback_price),
                    'fee': 0.0,
                    'timestamp': int(time.time() * 1000),
                    'order_id': order.get('id', '') if order else '',
                    'type': 'market'
                }

                print(f"🔧 Emitting fallback trade data: {fallback_trade}")
                self.trade_executed.emit(fallback_trade)

            except Exception as fallback_error:
                print(f"❌ Failed to emit fallback trade data: {fallback_error}")

    # Utility methods
    def is_demo_mode(self) -> bool:
        """Check if running in demo mode"""
        return self.demo_mode

    def get_connection_status(self) -> bool:
        """Get connection status"""
        return self.is_connected

    def get_exchange_info(self) -> Dict:
        """Get exchange information"""
        return {
            'name': self.exchange_name,
            'demo_mode': self.demo_mode,
            'connected': self.is_connected,
            'open_orders': len(self.open_orders),
            'open_positions': len([p for p in self.open_positions.values() if p['size'] != 0])
        }
