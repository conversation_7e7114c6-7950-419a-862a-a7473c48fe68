#!/usr/bin/env python3
"""
ScalperGPT Widget for Epinnox v6
Dedicated widget for ScalperGPT analysis display and controls
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, 
    QPushButton, QProgressBar, QGroupBox, QTableWidget, 
    QTableWidgetItem, QHeaderView, QCheckBox, QSlider
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont

from gui.matrix_theme import MatrixTheme

logger = logging.getLogger(__name__)

class ScalperGPTWidget(QWidget):
    """🎯 ScalperGPT Analysis Widget"""
    
    # Signals
    analysis_updated = Signal(dict)
    quality_threshold_changed = Signal(float)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("ScalperGPTWidget")
        
        # Initialize ScalperGPT instance
        self.scalper_gpt = None
        self.last_analysis = None
        self.analysis_history = []
        
        # Setup UI
        self.setup_ui()
        self.apply_matrix_theme()
        
        # Setup update timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_analysis_display)
        self.update_timer.start(5000)  # Update every 5 seconds
        
        # Initialize ScalperGPT
        self.initialize_scalper_gpt()
        
        logger.info("ScalperGPT Widget initialized")
    
    def setup_ui(self):
        """Setup the ScalperGPT widget UI"""
        layout = QVBoxLayout(self)
        
        # Header
        header_label = QLabel("🎯 SCALPERGPT ANALYSIS")
        header_label.setProperty("class", "header")
        layout.addWidget(header_label)
        
        # Quality Thresholds Group
        self.create_quality_thresholds_group(layout)
        
        # Analysis Status Group
        self.create_analysis_status_group(layout)
        
        # Opportunities Table Group
        self.create_opportunities_table_group(layout)
        
        # Controls Group
        self.create_controls_group(layout)
    
    def create_quality_thresholds_group(self, parent_layout):
        """Create quality thresholds control group"""
        group = QGroupBox("📊 QUALITY THRESHOLDS")
        layout = QGridLayout(group)
        
        # Spread Quality Threshold
        layout.addWidget(QLabel("Spread Quality:"), 0, 0)
        self.spread_quality_slider = QSlider(Qt.Horizontal)
        self.spread_quality_slider.setRange(50, 100)
        self.spread_quality_slider.setValue(70)  # Default 7.0
        self.spread_quality_slider.valueChanged.connect(self.on_spread_quality_changed)
        layout.addWidget(self.spread_quality_slider, 0, 1)
        
        self.spread_quality_label = QLabel("7.0")
        layout.addWidget(self.spread_quality_label, 0, 2)
        
        # Decision Quality Threshold
        layout.addWidget(QLabel("Decision Quality:"), 1, 0)
        self.decision_quality_slider = QSlider(Qt.Horizontal)
        self.decision_quality_slider.setRange(60, 100)
        self.decision_quality_slider.setValue(80)  # Default 8.0
        self.decision_quality_slider.valueChanged.connect(self.on_decision_quality_changed)
        layout.addWidget(self.decision_quality_slider, 1, 1)
        
        self.decision_quality_label = QLabel("8.0")
        layout.addWidget(self.decision_quality_label, 1, 2)
        
        parent_layout.addWidget(group)
    
    def create_analysis_status_group(self, parent_layout):
        """Create analysis status display group"""
        group = QGroupBox("📈 ANALYSIS STATUS")
        layout = QGridLayout(group)
        
        # Last Analysis Time
        layout.addWidget(QLabel("Last Analysis:"), 0, 0)
        self.last_analysis_label = QLabel("Not started")
        layout.addWidget(self.last_analysis_label, 0, 1)
        
        # Analysis Frequency
        layout.addWidget(QLabel("Analysis Interval:"), 1, 0)
        self.analysis_interval_label = QLabel("5 seconds")
        layout.addWidget(self.analysis_interval_label, 1, 1)
        
        # Current Symbol
        layout.addWidget(QLabel("Current Symbol:"), 2, 0)
        self.current_symbol_label = QLabel("None")
        layout.addWidget(self.current_symbol_label, 2, 1)
        
        # Analysis Status
        layout.addWidget(QLabel("Status:"), 3, 0)
        self.analysis_status_label = QLabel("Initializing...")
        layout.addWidget(self.analysis_status_label, 3, 1)
        
        parent_layout.addWidget(group)
    
    def create_opportunities_table_group(self, parent_layout):
        """Create opportunities table group"""
        group = QGroupBox("🎯 SCALPING OPPORTUNITIES")
        layout = QVBoxLayout(group)
        
        # Opportunities table
        self.opportunities_table = QTableWidget()
        self.opportunities_table.setColumnCount(6)
        self.opportunities_table.setHorizontalHeaderLabels([
            "Symbol", "Direction", "Entry", "Target", "Quality", "Confidence"
        ])
        self.opportunities_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.opportunities_table.setMaximumHeight(200)
        layout.addWidget(self.opportunities_table)
        
        # Summary labels
        summary_layout = QHBoxLayout()
        
        self.opportunities_count_label = QLabel("Opportunities: 0")
        summary_layout.addWidget(self.opportunities_count_label)
        
        self.avg_quality_label = QLabel("Avg Quality: 0.0")
        summary_layout.addWidget(self.avg_quality_label)
        
        layout.addLayout(summary_layout)
        parent_layout.addWidget(group)
    
    def create_controls_group(self, parent_layout):
        """Create controls group"""
        group = QGroupBox("🎮 CONTROLS")
        layout = QHBoxLayout(group)
        
        # Enable/Disable ScalperGPT
        self.enable_checkbox = QCheckBox("Enable ScalperGPT Analysis")
        self.enable_checkbox.setChecked(True)
        self.enable_checkbox.stateChanged.connect(self.on_enable_changed)
        layout.addWidget(self.enable_checkbox)
        
        # Manual Analysis Button
        self.manual_analysis_btn = QPushButton("Run Manual Analysis")
        self.manual_analysis_btn.clicked.connect(self.run_manual_analysis)
        layout.addWidget(self.manual_analysis_btn)
        
        # Clear History Button
        self.clear_history_btn = QPushButton("Clear History")
        self.clear_history_btn.clicked.connect(self.clear_analysis_history)
        layout.addWidget(self.clear_history_btn)
        
        parent_layout.addWidget(group)
    
    def apply_matrix_theme(self):
        """Apply Matrix theme styling"""
        try:
            MatrixTheme.apply_to_widget(self)
        except Exception as e:
            logger.warning(f"Could not apply Matrix theme: {e}")
    
    def initialize_scalper_gpt(self):
        """Initialize ScalperGPT instance"""
        try:
            from core.scalper_gpt import ScalperGPT
            
            self.scalper_gpt = ScalperGPT()
            self.analysis_status_label.setText("✅ Ready")
            logger.info("ScalperGPT instance initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize ScalperGPT: {e}")
            self.analysis_status_label.setText("❌ Failed to initialize")
    
    def update_analysis_display(self):
        """Update the analysis display with latest data"""
        try:
            if not self.scalper_gpt or not self.enable_checkbox.isChecked():
                return
            
            # Update last analysis time
            current_time = datetime.now().strftime("%H:%M:%S")
            self.last_analysis_label.setText(current_time)
            
            # Update opportunities table (placeholder)
            self.update_opportunities_table()
            
        except Exception as e:
            logger.error(f"Error updating analysis display: {e}")
    
    def update_opportunities_table(self):
        """Update the opportunities table"""
        try:
            # Clear existing rows
            self.opportunities_table.setRowCount(0)
            
            # Add placeholder data (in real implementation, this would come from ScalperGPT)
            opportunities = []  # Would get from self.scalper_gpt.get_current_opportunities()
            
            self.opportunities_table.setRowCount(len(opportunities))
            
            for row, opportunity in enumerate(opportunities):
                self.opportunities_table.setItem(row, 0, QTableWidgetItem(opportunity.get('symbol', '')))
                self.opportunities_table.setItem(row, 1, QTableWidgetItem(opportunity.get('direction', '')))
                self.opportunities_table.setItem(row, 2, QTableWidgetItem(f"{opportunity.get('entry_price', 0):.6f}"))
                self.opportunities_table.setItem(row, 3, QTableWidgetItem(f"{opportunity.get('target_price', 0):.6f}"))
                self.opportunities_table.setItem(row, 4, QTableWidgetItem(f"{opportunity.get('quality', 0):.1f}"))
                self.opportunities_table.setItem(row, 5, QTableWidgetItem(f"{opportunity.get('confidence', 0):.1f}%"))
            
            # Update summary
            self.opportunities_count_label.setText(f"Opportunities: {len(opportunities)}")
            
            if opportunities:
                avg_quality = sum(opp.get('quality', 0) for opp in opportunities) / len(opportunities)
                self.avg_quality_label.setText(f"Avg Quality: {avg_quality:.1f}")
            else:
                self.avg_quality_label.setText("Avg Quality: 0.0")
            
        except Exception as e:
            logger.error(f"Error updating opportunities table: {e}")
    
    def on_spread_quality_changed(self, value):
        """Handle spread quality threshold change"""
        quality = value / 10.0
        self.spread_quality_label.setText(f"{quality:.1f}")
        self.quality_threshold_changed.emit(quality)
        
        # Update ScalperGPT threshold if available
        if self.scalper_gpt:
            try:
                self.scalper_gpt.quality_thresholds['spread_quality'] = quality
            except Exception as e:
                logger.error(f"Error updating spread quality threshold: {e}")
    
    def on_decision_quality_changed(self, value):
        """Handle decision quality threshold change"""
        quality = value / 10.0
        self.decision_quality_label.setText(f"{quality:.1f}")
        self.quality_threshold_changed.emit(quality)
        
        # Update ScalperGPT threshold if available
        if self.scalper_gpt:
            try:
                self.scalper_gpt.quality_thresholds['decision_quality'] = quality
            except Exception as e:
                logger.error(f"Error updating decision quality threshold: {e}")
    
    def on_enable_changed(self, state):
        """Handle enable/disable state change"""
        enabled = state == Qt.Checked
        
        if enabled:
            self.update_timer.start(5000)
            self.analysis_status_label.setText("✅ Enabled")
        else:
            self.update_timer.stop()
            self.analysis_status_label.setText("⏸️ Disabled")
        
        logger.info(f"ScalperGPT analysis {'enabled' if enabled else 'disabled'}")
    
    def run_manual_analysis(self):
        """Run manual analysis"""
        try:
            if not self.scalper_gpt:
                logger.warning("ScalperGPT not initialized")
                return
            
            self.analysis_status_label.setText("🔄 Running manual analysis...")
            
            # In real implementation, this would trigger actual analysis
            # For now, just update the display
            self.update_analysis_display()
            
            self.analysis_status_label.setText("✅ Manual analysis complete")
            
        except Exception as e:
            logger.error(f"Error running manual analysis: {e}")
            self.analysis_status_label.setText("❌ Manual analysis failed")
    
    def clear_analysis_history(self):
        """Clear analysis history"""
        try:
            self.analysis_history.clear()
            self.opportunities_table.setRowCount(0)
            self.opportunities_count_label.setText("Opportunities: 0")
            self.avg_quality_label.setText("Avg Quality: 0.0")
            
            logger.info("ScalperGPT analysis history cleared")
            
        except Exception as e:
            logger.error(f"Error clearing analysis history: {e}")
    
    def get_current_thresholds(self) -> Dict[str, float]:
        """Get current quality thresholds"""
        return {
            'spread_quality': self.spread_quality_slider.value() / 10.0,
            'decision_quality': self.decision_quality_slider.value() / 10.0
        }
    
    def set_current_symbol(self, symbol: str):
        """Set the current trading symbol"""
        self.current_symbol_label.setText(symbol)
    
    def add_analysis_result(self, analysis: Dict[str, Any]):
        """Add analysis result to history"""
        try:
            self.analysis_history.append(analysis)
            
            # Keep only last 100 analyses
            if len(self.analysis_history) > 100:
                self.analysis_history = self.analysis_history[-100:]
            
            # Emit signal
            self.analysis_updated.emit(analysis)
            
        except Exception as e:
            logger.error(f"Error adding analysis result: {e}")
