"""
Autonomous LLM Integration for Epinnox Trading System
Integrates the unified LLM manager with the autonomous trading orchestrator
Provides 30-second decision loops and standardized AI decision making
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from .unified_llm_manager import UnifiedLL<PERSON>ana<PERSON>, LL<PERSON>rovider
from .standardized_prompt_handler import StandardizedPromptHandler, PromptTemplate, TradingDecision
from .scalper_gpt import ScalperGPT, ScalpingOpportunity
from .timer_coordinator import timer_coordinator, TimerPriority, StandardIntervals

logger = logging.getLogger(__name__)

class AutonomousLLMIntegration:
    """
    Autonomous LLM Integration that provides:
    1. 30-second decision loops for autonomous trading
    2. Standardized prompt handling across all LLM providers
    3. Health monitoring and automatic failover
    4. Integration with existing trading orchestrator
    5. Performance optimization and caching
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """Initialize the autonomous LLM integration"""
        self.config = config if config is not None else {}
        
        # Core components
        self.llm_manager = UnifiedLLMManager(self.config.get('llm_manager', {}))
        self.prompt_handler = StandardizedPromptHandler()

        # CRITICAL: Initialize ScalperGPT with quality thresholds
        self.scalper_gpt = ScalperGPT(
            llm_provider=self.llm_manager,
            config=self.config.get('scalper_gpt', {
                'analysis_interval': 5,
                'enable_ai_enhancement': True,
                'scalping_timeframe': '1m'
            })
        )
        
        # 🚨 SCALPING OPTIMIZATION: Faster decision loops for aggressive scalping
        self.decision_interval = self.config.get('decision_interval', 20)  # 🚀 Reduced from 30 to 20 seconds
        self.max_decisions_per_minute = self.config.get('max_decisions_per_minute', 5)  # 🚀 Increased from 3 to 5
        self.min_confidence_for_action = self.config.get('min_confidence_for_action', 0.60)  # 🚀 Reduced from 0.70 to 0.60

        # Timer coordination
        self.timer_registered = False
        self.use_timer_coordinator = self.config.get('use_timer_coordinator', True)

        # 🚨 SCALPING OPTIMIZATION: Relaxed ScalperGPT quality thresholds for more opportunities
        self.scalper_quality_thresholds = {
            'spread_quality': 6.0,      # 🚀 Reduced from 7.0 to 6.0 for more opportunities
            'decision_quality': 7.0,    # 🚀 Reduced from 8.0 to 7.0 for more opportunities
            'enable_scalper_filtering': True,
            'require_scalper_approval': True
        }
        
        # State tracking
        self.is_running = False
        self.decision_loop_task = None
        self.last_decisions = {}
        self.decision_history = []
        self.performance_metrics = {
            'total_decisions': 0,
            'successful_decisions': 0,
            'failed_decisions': 0,
            'scalper_opportunities': 0,
            'scalper_approved': 0,
            'avg_decision_time': 0.0,
            'avg_spread_quality': 0.0,
            'avg_decision_quality': 0.0,
            'last_decision_time': None
        }
        
        # Rate limiting
        self.decision_timestamps = []
        
        logger.info("[AUTONOMOUS_LLM] Autonomous LLM Integration initialized")
    
    async def initialize(self) -> bool:
        """Initialize the LLM integration system"""
        try:
            logger.info("[AUTONOMOUS_LLM] Initializing autonomous LLM integration...")
            
            # Initialize LLM manager
            success = await self.llm_manager.initialize()
            if not success:
                logger.error("[AUTONOMOUS_LLM] Failed to initialize LLM manager")
                return False
            
            logger.info("[AUTONOMOUS_LLM] Autonomous LLM integration initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[AUTONOMOUS_LLM] Failed to initialize: {e}")
            return False
    
    async def start_autonomous_decision_loop(self, trading_orchestrator):
        """Start the autonomous 30-second decision loop with timer coordination"""
        if self.is_running:
            logger.warning("[AUTONOMOUS_LLM] Decision loop already running")
            return

        self.is_running = True
        self.trading_orchestrator = trading_orchestrator

        # CRITICAL: Register with timer coordinator for synchronized execution
        if self.use_timer_coordinator and not self.timer_registered:
            success = timer_coordinator.register_timer(
                name="autonomous_llm_decision_loop",
                func=self._coordinated_decision_cycle,
                interval=self.decision_interval,
                priority=TimerPriority.HIGH,  # High priority for trading decisions
                max_execution_time=25.0  # Allow up to 25 seconds for decision cycle
            )

            if success:
                self.timer_registered = True
                logger.info(f"[AUTONOMOUS_LLM] Registered with timer coordinator (interval: {self.decision_interval}s)")
            else:
                logger.warning("[AUTONOMOUS_LLM] Failed to register with timer coordinator, using fallback")
                self.use_timer_coordinator = False

        # Start the decision loop (either coordinated or standalone)
        if not self.use_timer_coordinator:
            self.decision_loop_task = asyncio.create_task(self._decision_loop())
            logger.info(f"[AUTONOMOUS_LLM] Started standalone decision loop (interval: {self.decision_interval}s)")
        else:
            logger.info(f"[AUTONOMOUS_LLM] Using timer coordinator for decision loop (interval: {self.decision_interval}s)")
    
    async def stop_autonomous_decision_loop(self):
        """Stop the autonomous decision loop and unregister from timer coordinator"""
        self.is_running = False

        # Unregister from timer coordinator
        if self.timer_registered:
            timer_coordinator.unregister_timer("autonomous_llm_decision_loop")
            self.timer_registered = False
            logger.info("[AUTONOMOUS_LLM] Unregistered from timer coordinator")

        # Cancel standalone decision loop if running
        if hasattr(self, 'decision_loop_task') and self.decision_loop_task:
            self.decision_loop_task.cancel()
            try:
                await self.decision_loop_task
            except asyncio.CancelledError:
                pass

        logger.info("[AUTONOMOUS_LLM] Stopped autonomous decision loop")

    async def _coordinated_decision_cycle(self):
        """
        CRITICAL: Coordinated decision cycle for timer coordinator

        This method is called by the timer coordinator at precise 30-second intervals
        to ensure synchronized execution with other autonomous trading components.
        """
        try:
            if not self.is_running:
                return

            cycle_start_time = time.time()

            # Check rate limiting
            if not self._check_rate_limit():
                logger.debug("[AUTONOMOUS_LLM] Rate limit reached, skipping coordinated cycle")
                return

            # Get active symbols from trading orchestrator
            active_symbols = await self._get_active_symbols()

            if not active_symbols:
                logger.debug("[AUTONOMOUS_LLM] No active symbols, skipping coordinated cycle")
                return

            # Process each symbol
            for symbol in active_symbols:
                try:
                    await self._process_symbol_decision(symbol)
                except Exception as e:
                    logger.error(f"[AUTONOMOUS_LLM] Error processing {symbol} in coordinated cycle: {e}")

            # Update performance metrics
            cycle_time = time.time() - cycle_start_time
            self._update_performance_metrics(cycle_time)

            logger.debug(f"[AUTONOMOUS_LLM] Coordinated decision cycle completed in {cycle_time:.2f}s")

        except Exception as e:
            logger.error(f"[AUTONOMOUS_LLM] Error in coordinated decision cycle: {e}")

    async def _decision_loop(self):
        """Main autonomous decision loop"""
        logger.info("[AUTONOMOUS_LLM] Autonomous decision loop started")
        
        while self.is_running:
            try:
                loop_start_time = time.time()
                
                # Check rate limiting
                if not self._check_rate_limit():
                    logger.debug("[AUTONOMOUS_LLM] Rate limit reached, skipping decision cycle")
                    await asyncio.sleep(self.decision_interval)
                    continue
                
                # Get active symbols from trading orchestrator
                active_symbols = await self._get_active_symbols()
                
                if not active_symbols:
                    logger.debug("[AUTONOMOUS_LLM] No active symbols, skipping decision cycle")
                    await asyncio.sleep(self.decision_interval)
                    continue
                
                # Process each symbol
                for symbol in active_symbols:
                    try:
                        await self._process_symbol_decision(symbol)
                    except Exception as e:
                        logger.error(f"[AUTONOMOUS_LLM] Error processing {symbol}: {e}")
                
                # Update performance metrics
                loop_time = time.time() - loop_start_time
                self._update_performance_metrics(loop_time)
                
                # Wait for next cycle
                sleep_time = max(0, self.decision_interval - loop_time)
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"[AUTONOMOUS_LLM] Decision loop error: {e}")
                await asyncio.sleep(self.decision_interval)
        
        logger.info("[AUTONOMOUS_LLM] Autonomous decision loop stopped")
    
    async def _process_symbol_decision(self, symbol: str):
        """Process autonomous decision for a specific symbol"""
        try:
            decision_start_time = time.time()
            
            # Get market data and context
            market_data = await self._get_market_data(symbol)
            if not market_data:
                logger.debug(f"[AUTONOMOUS_LLM] No market data for {symbol}")
                return
            
            # Get current positions and risk metrics
            positions = await self._get_current_positions(symbol)
            risk_metrics = await self._get_risk_metrics()
            
            # Determine appropriate prompt template
            template = self._select_prompt_template(symbol, market_data, positions)
            
            # Build context for prompt
            context = self.prompt_handler.format_context(
                symbol=symbol,
                market_data=market_data,
                positions=positions,
                risk_metrics=risk_metrics
            )
            
            # Generate prompt
            prompt = self.prompt_handler.build_prompt(template, context)
            if not prompt:
                logger.error(f"[AUTONOMOUS_LLM] Failed to build prompt for {symbol}")
                return
            
            # Get LLM response
            llm_response = await self.llm_manager.generate_response(prompt, context)
            if not llm_response:
                logger.warning(f"[AUTONOMOUS_LLM] No LLM response for {symbol}")
                self.performance_metrics['failed_decisions'] += 1
                return
            
            # Parse response into trading decision
            trading_decision = self.prompt_handler.parse_response(llm_response.content, template)
            if not trading_decision:
                logger.warning(f"[AUTONOMOUS_LLM] Failed to parse decision for {symbol}")
                self.performance_metrics['failed_decisions'] += 1
                return
            
            # Validate decision confidence
            if trading_decision.confidence < self.min_confidence_for_action:
                logger.info(f"[AUTONOMOUS_LLM] {symbol} decision confidence {trading_decision.confidence:.1%} below threshold {self.min_confidence_for_action:.1%}")
                return

            # CRITICAL: ScalperGPT quality analysis with thresholds
            scalper_opportunity = None
            if self.scalper_quality_thresholds['enable_scalper_filtering']:
                scalper_opportunity = await self._analyze_with_scalper_gpt(symbol, market_data, trading_decision)

                if self.scalper_quality_thresholds['require_scalper_approval'] and not scalper_opportunity:
                    logger.info(f"[AUTONOMOUS_LLM] {symbol} decision rejected by ScalperGPT quality filters")
                    return

                if scalper_opportunity:
                    # Update decision with ScalperGPT insights
                    trading_decision = self._enhance_decision_with_scalper_insights(trading_decision, scalper_opportunity)
                    logger.info(f"[AUTONOMOUS_LLM] {symbol} enhanced with ScalperGPT: "
                               f"spread_quality={scalper_opportunity.spread_quality:.1f}, "
                               f"decision_quality={scalper_opportunity.decision_quality:.1f}")

            # Execute decision through trading orchestrator
            await self._execute_trading_decision(symbol, trading_decision, llm_response)
            
            # Update metrics and history
            decision_time = time.time() - decision_start_time
            self._record_decision(symbol, trading_decision, llm_response, decision_time, scalper_opportunity)
            
            logger.info(f"[AUTONOMOUS_LLM] {symbol} decision: {trading_decision.action} "
                       f"(confidence: {trading_decision.confidence:.1%}, "
                       f"provider: {llm_response.provider.value}, "
                       f"time: {decision_time:.2f}s)")
            
        except Exception as e:
            logger.error(f"[AUTONOMOUS_LLM] Error processing decision for {symbol}: {e}")
            self.performance_metrics['failed_decisions'] += 1
    
    def _select_prompt_template(self, symbol: str, market_data: Dict, positions: List) -> PromptTemplate:
        """Select appropriate prompt template based on current situation"""
        # Check for emergency conditions
        risk_metrics = market_data.get('risk_metrics', {})
        if risk_metrics.get('portfolio_risk', 0) > 80 or risk_metrics.get('daily_loss', 0) > 15:
            return PromptTemplate.EMERGENCY_RESPONSE
        
        # Check if we have positions to manage
        if positions:
            return PromptTemplate.POSITION_MANAGEMENT
        
        # Check if we're looking for entry opportunities
        if market_data.get('signal_strength', 0) > 7:
            return PromptTemplate.ENTRY_TIMING
        
        # Default to market analysis
        return PromptTemplate.MARKET_ANALYSIS
    
    async def _get_active_symbols(self) -> List[str]:
        """Get active trading symbols from orchestrator"""
        try:
            if hasattr(self.trading_orchestrator, 'get_active_symbols'):
                return await self.trading_orchestrator.get_active_symbols()
            elif hasattr(self.trading_orchestrator, 'active_symbols'):
                return self.trading_orchestrator.active_symbols
            else:
                # Default symbols
                return ['DOGE/USDT:USDT', 'BTC/USDT:USDT']
        except Exception as e:
            logger.error(f"[AUTONOMOUS_LLM] Error getting active symbols: {e}")
            return []
    
    async def _get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get market data for symbol"""
        try:
            if hasattr(self.trading_orchestrator, 'get_market_data'):
                return await self.trading_orchestrator.get_market_data(symbol)
            else:
                # Fallback to basic market data
                return {
                    'symbol': symbol,
                    'price': 1000.0,  # Placeholder
                    'change_24h': 0.0,
                    'volume_24h': 1000000,
                    'spread': 0.1,
                    'indicators': {},
                    'context': {}
                }
        except Exception as e:
            logger.error(f"[AUTONOMOUS_LLM] Error getting market data for {symbol}: {e}")
            return None
    
    async def _get_current_positions(self, symbol: str) -> List[Dict]:
        """Get current positions for symbol"""
        try:
            if hasattr(self.trading_orchestrator, 'get_positions'):
                all_positions = await self.trading_orchestrator.get_positions()
                return [pos for pos in all_positions if pos.get('symbol') == symbol]
            else:
                return []
        except Exception as e:
            logger.error(f"[AUTONOMOUS_LLM] Error getting positions for {symbol}: {e}")
            return []
    
    async def _get_risk_metrics(self) -> Dict[str, Any]:
        """Get current risk metrics"""
        try:
            if hasattr(self.trading_orchestrator, 'get_risk_metrics'):
                return await self.trading_orchestrator.get_risk_metrics()
            else:
                # Default risk metrics
                return {
                    'max_position_size': 30,
                    'max_daily_loss': 20,
                    'portfolio_risk': 0,
                    'daily_pnl': 0,
                    'unrealized_pnl': 0
                }
        except Exception as e:
            logger.error(f"[AUTONOMOUS_LLM] Error getting risk metrics: {e}")
            return {}
    
    async def _execute_trading_decision(self, symbol: str, decision: TradingDecision, llm_response):
        """Execute trading decision through orchestrator"""
        try:
            if hasattr(self.trading_orchestrator, 'execute_llm_decision'):
                await self.trading_orchestrator.execute_llm_decision(symbol, decision, llm_response)
            else:
                logger.warning(f"[AUTONOMOUS_LLM] No execution method available for {symbol} decision")
        except Exception as e:
            logger.error(f"[AUTONOMOUS_LLM] Error executing decision for {symbol}: {e}")
    
    def _check_rate_limit(self) -> bool:
        """Check if we're within rate limits"""
        current_time = time.time()
        
        # Remove old timestamps (older than 1 minute)
        self.decision_timestamps = [ts for ts in self.decision_timestamps if current_time - ts < 60]
        
        # Check if we can make another decision
        if len(self.decision_timestamps) >= self.max_decisions_per_minute:
            return False
        
        # Add current timestamp
        self.decision_timestamps.append(current_time)
        return True
    
    def _record_decision(self, symbol: str, decision: TradingDecision, llm_response, decision_time: float,
                        scalper_opportunity: Optional[ScalpingOpportunity] = None):
        """Record decision in history and update metrics"""
        # Update last decisions
        self.last_decisions[symbol] = {
            'decision': decision,
            'llm_response': llm_response,
            'timestamp': datetime.now(),
            'decision_time': decision_time,
            'scalper_opportunity': scalper_opportunity
        }
        
        # Add to history (keep last 100)
        decision_record = {
            'symbol': symbol,
            'decision': decision.to_dict(),
            'provider': llm_response.provider.value,
            'confidence': llm_response.confidence,
            'response_time': llm_response.response_time,
            'decision_time': decision_time,
            'timestamp': datetime.now().isoformat()
        }

        # Add ScalperGPT metrics if available
        if scalper_opportunity:
            decision_record['scalper_gpt'] = {
                'spread_quality': scalper_opportunity.spread_quality,
                'decision_quality': scalper_opportunity.decision_quality,
                'scalper_confidence': scalper_opportunity.confidence,
                'risk_reward_ratio': scalper_opportunity.risk_reward_ratio,
                'enhanced': True
            }

            # Update running averages for ScalperGPT metrics
            self._update_scalper_metrics(scalper_opportunity)

        self.decision_history.append(decision_record)
        
        if len(self.decision_history) > 100:
            self.decision_history.pop(0)
        
        # Update performance metrics
        self.performance_metrics['total_decisions'] += 1
        self.performance_metrics['successful_decisions'] += 1
        self.performance_metrics['last_decision_time'] = datetime.now()
    
    def _update_performance_metrics(self, loop_time: float):
        """Update performance metrics"""
        if self.performance_metrics['avg_decision_time'] == 0:
            self.performance_metrics['avg_decision_time'] = loop_time
        else:
            # Exponential moving average
            self.performance_metrics['avg_decision_time'] = (
                self.performance_metrics['avg_decision_time'] * 0.9 + loop_time * 0.1
            )

    def _update_scalper_metrics(self, scalper_opportunity: ScalpingOpportunity):
        """Update running averages for ScalperGPT metrics"""
        try:
            # Get recent ScalperGPT decisions for averaging
            recent_scalper_decisions = [
                record for record in self.decision_history[-50:]  # Last 50 decisions
                if 'scalper_gpt' in record
            ]

            if recent_scalper_decisions:
                # Update average spread quality
                spread_qualities = [record['scalper_gpt']['spread_quality'] for record in recent_scalper_decisions]
                self.performance_metrics['avg_spread_quality'] = sum(spread_qualities) / len(spread_qualities)

                # Update average decision quality
                decision_qualities = [record['scalper_gpt']['decision_quality'] for record in recent_scalper_decisions]
                self.performance_metrics['avg_decision_quality'] = sum(decision_qualities) / len(decision_qualities)
            else:
                # First ScalperGPT decision
                self.performance_metrics['avg_spread_quality'] = scalper_opportunity.spread_quality
                self.performance_metrics['avg_decision_quality'] = scalper_opportunity.decision_quality

        except Exception as e:
            logger.error(f"[SCALPER_GPT] Error updating metrics: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of autonomous LLM integration"""
        return {
            'is_running': self.is_running,
            'decision_interval': self.decision_interval,
            'current_provider': self.llm_manager.current_provider.value if self.llm_manager.current_provider else None,
            'performance_metrics': self.performance_metrics,
            'llm_health': self.llm_manager.get_health_status(),
            'recent_decisions': len(self.decision_history),
            'last_decisions': {
                symbol: {
                    'action': data['decision'].action,
                    'confidence': data['decision'].confidence,
                    'timestamp': data['timestamp'].isoformat()
                }
                for symbol, data in self.last_decisions.items()
            }
        }
    
    async def _analyze_with_scalper_gpt(self, symbol: str, market_data: Dict[str, Any],
                                      trading_decision: TradingDecision) -> Optional[ScalpingOpportunity]:
        """
        CRITICAL: Analyze trading decision with ScalperGPT quality thresholds

        Args:
            symbol: Trading symbol
            market_data: Market data for analysis
            trading_decision: LLM trading decision to validate

        Returns:
            ScalpingOpportunity if quality thresholds are met, None otherwise
        """
        try:
            # Analyze scalping opportunity with ScalperGPT
            scalper_opportunity = await self.scalper_gpt.analyze_scalping_opportunity(symbol, market_data)

            if not scalper_opportunity:
                logger.debug(f"[SCALPER_GPT] No opportunity detected for {symbol}")
                return None

            # Check quality thresholds
            spread_quality_ok = scalper_opportunity.spread_quality >= self.scalper_quality_thresholds['spread_quality']
            decision_quality_ok = scalper_opportunity.decision_quality >= self.scalper_quality_thresholds['decision_quality']

            if spread_quality_ok and decision_quality_ok:
                self.performance_metrics['scalper_approved'] += 1
                logger.info(f"[SCALPER_GPT] ✅ {symbol} approved: "
                           f"spread_quality={scalper_opportunity.spread_quality:.1f} >= {self.scalper_quality_thresholds['spread_quality']}, "
                           f"decision_quality={scalper_opportunity.decision_quality:.1f} >= {self.scalper_quality_thresholds['decision_quality']}")
                return scalper_opportunity
            else:
                logger.info(f"[SCALPER_GPT] ❌ {symbol} rejected: "
                           f"spread_quality={scalper_opportunity.spread_quality:.1f} (need >= {self.scalper_quality_thresholds['spread_quality']}), "
                           f"decision_quality={scalper_opportunity.decision_quality:.1f} (need >= {self.scalper_quality_thresholds['decision_quality']})")
                return None

        except Exception as e:
            logger.error(f"[SCALPER_GPT] Error analyzing {symbol}: {e}")
            return None
        finally:
            self.performance_metrics['scalper_opportunities'] += 1

    def _enhance_decision_with_scalper_insights(self, trading_decision: TradingDecision,
                                              scalper_opportunity: ScalpingOpportunity) -> TradingDecision:
        """
        Enhance LLM trading decision with ScalperGPT insights

        Args:
            trading_decision: Original LLM decision
            scalper_opportunity: ScalperGPT analysis

        Returns:
            Enhanced trading decision
        """
        try:
            # Create enhanced decision with ScalperGPT insights
            enhanced_decision = TradingDecision(
                action=scalper_opportunity.direction,  # Use ScalperGPT direction
                confidence=min(0.95, (trading_decision.confidence + scalper_opportunity.confidence) / 2),  # Average confidence
                reasoning=f"{trading_decision.reasoning} | ScalperGPT: {scalper_opportunity.reasoning}",
                symbol=trading_decision.symbol,
                entry_price=scalper_opportunity.entry_price,
                stop_loss=scalper_opportunity.stop_loss,
                take_profit=scalper_opportunity.target_price,
                position_size=trading_decision.position_size,
                leverage=trading_decision.leverage,
                metadata={
                    **trading_decision.metadata,
                    'scalper_gpt_enhanced': True,
                    'spread_quality': scalper_opportunity.spread_quality,
                    'decision_quality': scalper_opportunity.decision_quality,
                    'scalper_confidence': scalper_opportunity.confidence,
                    'risk_reward_ratio': scalper_opportunity.risk_reward_ratio
                }
            )

            logger.debug(f"[SCALPER_GPT] Enhanced decision for {trading_decision.symbol}: "
                        f"confidence {trading_decision.confidence:.1%} -> {enhanced_decision.confidence:.1%}")

            return enhanced_decision

        except Exception as e:
            logger.error(f"[SCALPER_GPT] Error enhancing decision: {e}")
            return trading_decision

    def get_scalper_gpt_metrics(self) -> Dict[str, Any]:
        """Get ScalperGPT performance metrics"""
        try:
            scalper_metrics = self.scalper_gpt.get_performance_metrics()

            # Calculate approval rate
            approval_rate = 0.0
            if self.performance_metrics['scalper_opportunities'] > 0:
                approval_rate = self.performance_metrics['scalper_approved'] / self.performance_metrics['scalper_opportunities']

            return {
                'scalper_gpt_metrics': scalper_metrics,
                'integration_metrics': {
                    'opportunities_analyzed': self.performance_metrics['scalper_opportunities'],
                    'opportunities_approved': self.performance_metrics['scalper_approved'],
                    'approval_rate': approval_rate,
                    'avg_spread_quality': self.performance_metrics['avg_spread_quality'],
                    'avg_decision_quality': self.performance_metrics['avg_decision_quality']
                },
                'quality_thresholds': self.scalper_quality_thresholds
            }

        except Exception as e:
            logger.error(f"[SCALPER_GPT] Error getting metrics: {e}")
            return {}

    async def shutdown(self):
        """Shutdown the autonomous LLM integration"""
        await self.stop_autonomous_decision_loop()
        await self.llm_manager.shutdown()

        logger.info("[AUTONOMOUS_LLM] Autonomous LLM integration shutdown complete")
