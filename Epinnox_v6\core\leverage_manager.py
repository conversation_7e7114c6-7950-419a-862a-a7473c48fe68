"""
Dynamic Leverage Manager for Epinnox v6

This module handles dynamic leverage fetching, risk-based leverage scaling,
and intelligent position sizing with leverage considerations.
"""

import logging
from typing import Dict, Optional, Tuple, Any
from dataclasses import dataclass
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class LeverageInfo:
    """Container for leverage information"""
    symbol: str
    max_leverage: float
    recommended_leverage: float
    leverage_reasoning: str
    risk_multiplier: float
    volatility_adjustment: float
    regime_adjustments: Dict[str, float] = None  # 🚨 ENHANCED: Store regime adjustments

@dataclass
class PositionSizeResult:
    """Container for position sizing results with leverage"""
    symbol: str
    position_units: float
    position_usd: float
    effective_leverage: float
    max_available_leverage: float
    recommended_leverage: float
    risk_per_trade_usd: float
    stop_loss_price: float
    take_profit_price: float
    leverage_reasoning: str
    risk_warnings: list

class DynamicLeverageManager:
    """
    Manages dynamic leverage fetching and intelligent position sizing
    """
    
    def __init__(self, base_balance: float = 50.0):
        """
        Initialize leverage manager
        
        Args:
            base_balance: Base account balance in USD
        """
        self.base_balance = base_balance
        
        # Risk management parameters
        self.max_risk_per_trade_pct = 5.0  # 5% of balance max risk
        self.confidence_leverage_scaling = {
            'high': 0.8,      # Use 80% of max leverage for high confidence
            'medium': 0.6,    # Use 60% of max leverage for medium confidence
            'low': 0.3        # Use 30% of max leverage for low confidence
        }
        
        # Market regime leverage adjustments
        self.regime_leverage_multipliers = {
            'strong_trend': 1.0,      # Full leverage in strong trends
            'trending': 0.8,          # Reduced leverage in normal trends
            'low_volatility': 0.9,    # Slightly reduced in low vol
            'high_volatility': 0.5,   # Significantly reduced in high vol
            'uncertain': 0.3,         # Very conservative in uncertain markets
            'range_bound': 0.6        # Moderate leverage in ranging markets
        }
        
        # Symbol-specific risk multipliers
        self.symbol_risk_multipliers = {
            'BTC': 0.8,    # Bitcoin - slightly conservative
            'ETH': 0.8,    # Ethereum - slightly conservative  
            'DOGE': 1.0,   # Dogecoin - standard risk
            'ADA': 1.1,    # Cardano - slightly higher risk
            'SOL': 1.1,    # Solana - slightly higher risk
            'MATIC': 1.2,  # Polygon - higher risk
            'SHIB': 1.3,   # Shiba - highest risk
        }
        
        logger.info(f"Dynamic Leverage Manager initialized with ${base_balance} balance")
    
    def fetch_symbol_leverage(self, exchange, symbol: str) -> Optional[float]:
        """
        Fetch maximum leverage for a symbol from exchange
        
        Args:
            exchange: CCXT exchange instance
            symbol: Trading symbol (e.g., 'DOGE/USDT:USDT')
            
        Returns:
            Maximum leverage or None if not found
        """
        try:
            # Load markets if not already loaded
            if not hasattr(exchange, 'markets') or not exchange.markets:
                exchange.load_markets()
            
            # Get market info for the symbol
            if symbol in exchange.markets:
                market = exchange.markets[symbol]
                
                # Try to get leverage from market info
                if 'limits' in market and 'leverage' in market['limits']:
                    max_leverage = market['limits']['leverage']['max']
                    if max_leverage:
                        logger.info(f"Fetched max leverage for {symbol}: {max_leverage}x")
                        return float(max_leverage)
                
                # Try alternative fields
                if 'info' in market:
                    info = market['info']
                    # HTX specific fields
                    if 'leverage-ratio' in info:
                        max_leverage = float(info['leverage-ratio'])
                        logger.info(f"Fetched max leverage for {symbol}: {max_leverage}x")
                        return max_leverage
                    
                    if 'maxLeverage' in info:
                        max_leverage = float(info['maxLeverage'])
                        logger.info(f"Fetched max leverage for {symbol}: {max_leverage}x")
                        return max_leverage
            
            # Fallback: try to fetch from exchange-specific API
            if hasattr(exchange, 'fetch_trading_fees'):
                try:
                    fees = exchange.fetch_trading_fees([symbol])
                    if symbol in fees and 'leverage' in fees[symbol]:
                        max_leverage = fees[symbol]['leverage']
                        logger.info(f"Fetched max leverage for {symbol}: {max_leverage}x")
                        return float(max_leverage)
                except Exception as e:
                    logger.debug(f"Could not fetch trading fees for leverage: {e}")
            
            # Default leverage based on symbol type
            default_leverage = self._get_default_leverage(symbol)
            logger.warning(f"Could not fetch leverage for {symbol}, using default: {default_leverage}x")
            return default_leverage
            
        except Exception as e:
            logger.error(f"Error fetching leverage for {symbol}: {e}")
            return self._get_default_leverage(symbol)
    
    def _get_default_leverage(self, symbol: str) -> float:
        """Get default leverage based on symbol type"""
        if ':USDT' in symbol or ':USD' in symbol:
            # Futures symbols typically have higher leverage
            if 'BTC' in symbol or 'ETH' in symbol:
                return 100.0  # Major coins
            elif 'DOGE' in symbol:
                return 75.0   # DOGE specific
            else:
                return 50.0   # Other altcoins
        else:
            # Spot symbols have lower leverage
            return 10.0
    
    def calculate_recommended_leverage(self,
                                     max_leverage: float,
                                     confidence: float,
                                     market_regime: str,
                                     symbol: str,
                                     volatility: float = None,
                                     regime_adjustments: Dict[str, float] = None) -> LeverageInfo:
        """
        Calculate recommended leverage based on multiple factors
        
        Args:
            max_leverage: Maximum available leverage
            confidence: Signal confidence (0-1)
            market_regime: Current market regime
            symbol: Trading symbol
            volatility: Current volatility (optional)
            
        Returns:
            LeverageInfo with recommended leverage and reasoning
        """
        try:
            reasoning_parts = []
            
            # 1. Confidence-based scaling
            if confidence >= 0.8:
                confidence_multiplier = self.confidence_leverage_scaling['high']
                reasoning_parts.append(f"High confidence ({confidence:.1%})")
            elif confidence >= 0.6:
                confidence_multiplier = self.confidence_leverage_scaling['medium']
                reasoning_parts.append(f"Medium confidence ({confidence:.1%})")
            else:
                confidence_multiplier = self.confidence_leverage_scaling['low']
                reasoning_parts.append(f"Low confidence ({confidence:.1%})")
            
            # 2. Market regime adjustment (🚨 ENHANCED: Use regime-based adjustments)
            if regime_adjustments and 'leverage_multiplier' in regime_adjustments:
                regime_multiplier = regime_adjustments['leverage_multiplier']
                reasoning_parts.append(f"Regime-based leverage: {regime_multiplier:.2f}x")
                logger.info(f"🎯 Applied regime-based leverage adjustment: {regime_multiplier:.2f}x for {market_regime}")
            else:
                regime_multiplier = self.regime_leverage_multipliers.get(market_regime, 0.5)
                reasoning_parts.append(f"Market regime: {market_regime}")
                logger.debug(f"Using static regime multiplier: {regime_multiplier:.2f}x for {market_regime}")
            
            # 3. Symbol-specific risk adjustment
            base_symbol = symbol.split('/')[0] if '/' in symbol else symbol
            symbol_multiplier = self.symbol_risk_multipliers.get(base_symbol, 1.0)
            if symbol_multiplier != 1.0:
                reasoning_parts.append(f"Symbol risk: {base_symbol} ({symbol_multiplier:.1f}x)")
            
            # 4. Volatility adjustment
            volatility_multiplier = 1.0
            if volatility is not None:
                if volatility > 0.05:  # High volatility (>5%)
                    volatility_multiplier = 0.6
                    reasoning_parts.append(f"High volatility ({volatility:.1%})")
                elif volatility < 0.01:  # Low volatility (<1%)
                    volatility_multiplier = 1.1
                    reasoning_parts.append(f"Low volatility ({volatility:.1%})")
            
            # Calculate final leverage
            total_multiplier = (confidence_multiplier * 
                              regime_multiplier * 
                              symbol_multiplier * 
                              volatility_multiplier)
            
            recommended_leverage = min(max_leverage * total_multiplier, max_leverage)
            recommended_leverage = max(1.0, recommended_leverage)  # Minimum 1x leverage
            
            # Create reasoning string
            leverage_reasoning = f"Leverage scaling: {total_multiplier:.2f} = " + \
                               f"confidence({confidence_multiplier:.1f}) × " + \
                               f"regime({regime_multiplier:.1f}) × " + \
                               f"symbol({symbol_multiplier:.1f}) × " + \
                               f"volatility({volatility_multiplier:.1f}). " + \
                               f"Factors: {', '.join(reasoning_parts)}"
            
            return LeverageInfo(
                symbol=symbol,
                max_leverage=max_leverage,
                recommended_leverage=recommended_leverage,
                leverage_reasoning=leverage_reasoning,
                risk_multiplier=total_multiplier,
                volatility_adjustment=volatility_multiplier,
                regime_adjustments=regime_adjustments  # 🚨 ENHANCED: Include regime adjustments
            )
            
        except Exception as e:
            logger.error(f"Error calculating recommended leverage: {e}")
            # Conservative fallback
            return LeverageInfo(
                symbol=symbol,
                max_leverage=max_leverage,
                recommended_leverage=min(5.0, max_leverage),
                leverage_reasoning=f"Error in calculation, using conservative 5x leverage: {e}",
                risk_multiplier=0.1,
                volatility_adjustment=1.0
            )
    
    def calculate_position_size_with_leverage(self,
                                            leverage_info: LeverageInfo,
                                            current_price: float,
                                            signal_direction: str,
                                            stop_loss_pct: float = 0.05,
                                            take_profit_pct: float = 0.10,
                                            liquidity_score: float = 1.0) -> PositionSizeResult:
        """
        Calculate position size considering leverage and risk management
        
        Args:
            leverage_info: Leverage information
            current_price: Current market price
            signal_direction: LONG, SHORT, or WAIT
            stop_loss_pct: Stop loss percentage (default 5%)
            take_profit_pct: Take profit percentage (default 10%)
            liquidity_score: Market liquidity score (0-1)
            
        Returns:
            PositionSizeResult with detailed position information
        """
        try:
            warnings = []
            
            # Calculate maximum risk per trade (🚨 ENHANCED: Apply regime position size adjustment)
            base_risk_pct = self.max_risk_per_trade_pct

            # Apply regime-based position size adjustment if available
            if hasattr(leverage_info, 'regime_adjustments') and leverage_info.regime_adjustments:
                position_multiplier = leverage_info.regime_adjustments.get('position_size_multiplier', 1.0)
                adjusted_risk_pct = base_risk_pct * position_multiplier
                logger.info(f"🎯 Applied regime-based position size adjustment: {position_multiplier:.2f}x ({base_risk_pct:.1f}% -> {adjusted_risk_pct:.1f}%)")
            else:
                adjusted_risk_pct = base_risk_pct

            max_risk_usd = self.base_balance * (adjusted_risk_pct / 100)
            
            # Adjust for liquidity
            liquidity_adjustment = min(1.0, liquidity_score + 0.2)  # Minimum 20% of normal size
            if liquidity_score < 0.5:
                warnings.append(f"Low liquidity ({liquidity_score:.2f}) - position size reduced")
            
            # Calculate position size based on risk
            # Position size = (Risk Amount × Leverage) / (Stop Loss % × Price)
            effective_leverage = leverage_info.recommended_leverage * liquidity_adjustment
            
            if signal_direction == 'WAIT':
                # No position for WAIT signals
                position_units = 0.0
                position_usd = 0.0
                risk_per_trade = 0.0
                stop_loss_price = current_price
                take_profit_price = current_price
            else:
                # Calculate position size
                position_usd = (max_risk_usd * effective_leverage) / stop_loss_pct
                position_usd = min(position_usd, self.base_balance * effective_leverage)  # Don't exceed leveraged balance
                
                position_units = position_usd / current_price
                risk_per_trade = position_usd * stop_loss_pct / effective_leverage
                
                # Calculate stop loss and take profit prices
                if signal_direction == 'LONG':
                    stop_loss_price = current_price * (1 - stop_loss_pct)
                    take_profit_price = current_price * (1 + take_profit_pct)
                else:  # SHORT
                    stop_loss_price = current_price * (1 + stop_loss_pct)
                    take_profit_price = current_price * (1 - take_profit_pct)
            
            # Add warnings based on leverage usage
            leverage_usage_pct = (effective_leverage / leverage_info.max_leverage) * 100
            if leverage_usage_pct > 80:
                warnings.append(f"High leverage usage ({leverage_usage_pct:.1f}% of max)")
            elif leverage_usage_pct < 20:
                warnings.append(f"Conservative leverage usage ({leverage_usage_pct:.1f}% of max)")
            
            # Risk warnings
            risk_pct = (risk_per_trade / self.base_balance) * 100
            if risk_pct > self.max_risk_per_trade_pct:
                warnings.append(f"Risk exceeds limit ({risk_pct:.1f}% > {self.max_risk_per_trade_pct}%)")
            
            return PositionSizeResult(
                symbol=leverage_info.symbol,
                position_units=position_units,
                position_usd=position_usd,
                effective_leverage=effective_leverage,
                max_available_leverage=leverage_info.max_leverage,
                recommended_leverage=leverage_info.recommended_leverage,
                risk_per_trade_usd=risk_per_trade,
                stop_loss_price=stop_loss_price,
                take_profit_price=take_profit_price,
                leverage_reasoning=leverage_info.leverage_reasoning,
                risk_warnings=warnings
            )
            
        except Exception as e:
            logger.error(f"Error calculating position size with leverage: {e}")
            # Return safe defaults
            return PositionSizeResult(
                symbol=leverage_info.symbol,
                position_units=0.0,
                position_usd=0.0,
                effective_leverage=1.0,
                max_available_leverage=leverage_info.max_leverage,
                recommended_leverage=1.0,
                risk_per_trade_usd=0.0,
                stop_loss_price=current_price,
                take_profit_price=current_price,
                leverage_reasoning=f"Error in calculation: {e}",
                risk_warnings=[f"Calculation error: {e}"]
            )
    
    def update_balance(self, new_balance: float):
        """Update the base balance"""
        self.base_balance = new_balance
        logger.info(f"Updated base balance to ${new_balance}")
    
    def get_leverage_summary(self, position_result: PositionSizeResult) -> Dict[str, Any]:
        """Get a summary of leverage information for logging/display"""
        return {
            'max_available_leverage': f"{position_result.max_available_leverage:.1f}x",
            'recommended_leverage': f"{position_result.recommended_leverage:.1f}x",
            'effective_leverage': f"{position_result.effective_leverage:.1f}x",
            'position_size_units': f"{position_result.position_units:.2f}",
            'position_size_usd': f"${position_result.position_usd:.2f}",
            'risk_per_trade': f"${position_result.risk_per_trade_usd:.2f}",
            'stop_loss_price': f"${position_result.stop_loss_price:.4f}",
            'take_profit_price': f"${position_result.take_profit_price:.4f}",
            'leverage_reasoning': position_result.leverage_reasoning,
            'risk_warnings': position_result.risk_warnings
        }
