"""
LLM Action Executors - Execute trading actions based on LLM decisions
Handles position management, emergency responses, and trade execution
"""

import time
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

# Import the intelligent limit order manager
try:
    from trading.intelligent_limit_order_manager import IntelligentLimitOrderManager
except ImportError:
    IntelligentLimitOrderManager = None

logger = logging.getLogger(__name__)

class LLMActionExecutors:
    """
    Execute trading actions based on LLM prompt responses
    Handles all types of trading decisions with proper risk management
    """
    
    def __init__(self, trading_interface, main_window):
        self.trading_interface = trading_interface
        self.main_window = main_window
        self.execution_log = []

        # 🚀 ENHANCED: Initialize intelligent limit order manager
        self.limit_order_manager = None
        if IntelligentLimitOrderManager and hasattr(trading_interface, 'real_trading'):
            try:
                # Get live data manager from main window
                live_data_manager = getattr(main_window, 'live_data_manager', None)
                trading_engine = getattr(trading_interface.real_trading, 'trading_engine', None)

                if live_data_manager and trading_engine:
                    self.limit_order_manager = IntelligentLimitOrderManager(
                        trading_engine,
                        live_data_manager
                    )
                    logger.info("✅ Intelligent Limit Order Manager initialized")
                else:
                    logger.warning("⚠️ Missing trading engine or live data manager - using fallback")
            except Exception as e:
                logger.error(f"Failed to initialize limit order manager: {e}")
        else:
            logger.warning("⚠️ Intelligent Limit Order Manager not available")

        # Safety limits
        self.max_daily_trades = 50
        self.max_concurrent_positions = 3
        self.max_daily_loss = 100.0  # USD

        # Execution tracking
        self.daily_trades = 0
        self.daily_loss = 0.0
        self.last_trade_time = 0

        # 🚀 ENHANCED: Professional scalping configuration with STRICT LIMIT ORDER ENFORCEMENT
        self.scalping_config = {
            'use_limit_orders_only': True,  # CRITICAL: No market orders allowed
            'enforce_limit_orders': True,   # CRITICAL: Strict enforcement
            'reject_market_orders': True,   # CRITICAL: Reject any market order attempts
            'order_timeout_seconds': 60,    # Quick timeout for scalping
            'max_spread_pct': 0.2,         # Only trade when spread < 0.2%
            'aggressive_fill_mode': True,   # Place orders aggressively within spread
            'auto_cancel_stale_orders': True,  # Cancel unfilled orders automatically
            'order_replacement_enabled': True,  # Replace orders with better prices
        }

        logger.info("🚀 LLM Action Executors initialized with intelligent limit order system")
    
    def execute_emergency_actions(self, response: Dict[str, Any], context) -> bool:
        """Execute emergency response actions"""
        try:
            action = response.get('ACTION', 'MONITOR')
            priority = response.get('PRIORITY', 'MEDIUM')
            
            logger.warning(f"Executing emergency action: {action} (Priority: {priority})")
            
            if action == 'CLOSE_ALL_POSITIONS':
                return self.close_all_positions_emergency()
            
            elif action == 'CLOSE_LOSING':
                return self.close_losing_positions()
            
            elif action == 'HEDGE':
                return self.create_hedge_positions(context)
            
            elif action == 'REDUCE_SIZE':
                return self.reduce_position_sizes()
            
            elif action == 'MONITOR':
                logger.info("Emergency monitoring - no immediate action required")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error executing emergency actions: {e}")
            return False
    
    def execute_position_actions(self, response: Dict[str, Any], context) -> bool:
        """Execute position management actions"""
        try:
            action = response.get('ACTION', 'HOLD')
            urgency = response.get('URGENCY', 'LOW')
            
            logger.info(f"Executing position action: {action} (Urgency: {urgency})")
            
            if action == 'CLOSE':
                return self.close_position_full(context)
            
            elif action == 'PARTIAL_CLOSE':
                close_percentage = response.get('CLOSE_PERCENTAGE', 50)
                return self.close_position_partial(context, close_percentage)
            
            elif action == 'HOLD':
                # Update stop loss and take profit if provided
                return self.update_position_levels(response, context)
            
            return True
            
        except Exception as e:
            logger.error(f"Error executing position actions: {e}")
            return False
    
    def execute_profit_actions(self, response: Dict[str, Any], context) -> bool:
        """Execute profit optimization actions"""
        try:
            action = response.get('ACTION', 'HOLD')
            
            logger.info(f"Executing profit action: {action}")
            
            if action == 'FULL_CLOSE':
                return self.close_position_full(context)
            
            elif action == 'PARTIAL_CLOSE':
                close_percentage = response.get('CLOSE_PERCENTAGE', 50)
                trail_stop = response.get('TRAIL_STOP', False)
                
                success = self.close_position_partial(context, close_percentage)
                
                if success and trail_stop:
                    self.set_trailing_stop(response, context)
                
                return success
            
            elif action == 'HOLD':
                # Set trailing stop if requested
                if response.get('TRAIL_STOP', False):
                    return self.set_trailing_stop(response, context)
            
            return True
            
        except Exception as e:
            logger.error(f"Error executing profit actions: {e}")
            return False
    
    def execute_entry_actions(self, response: Dict[str, Any], context) -> bool:
        """Execute entry timing actions"""
        try:
            action = response.get('ACTION', 'WAIT')
            entry_type = response.get('ENTRY_TYPE', 'LIMIT')

            if action != 'ENTER_NOW':
                return True

            # 🚨 CRITICAL FIX: Check aggregated vote decision FIRST before any other checks
            if hasattr(context, 'aggregated_decision'):
                aggregated_decision = context.aggregated_decision
                if aggregated_decision and aggregated_decision.decision.value == 'WAIT':
                    logger.warning(f"🛑 CONFLICT DETECTED: Entry timing says ENTER_NOW but aggregated decision is WAIT ({aggregated_decision.confidence:.1f}%)")
                    logger.warning(f"🛑 Aggregated reasoning: {aggregated_decision.reasoning}")
                    logger.warning(f"🛑 BLOCKING ORDER PLACEMENT - Aggregated decision takes precedence")
                    return True  # Don't place order, but don't fail

            logger.info(f"Executing entry action: {action} ({entry_type} order)")

            # 🚨 DETAILED LOGGING: Track balance and position state before entry
            self.track_balance_update(context)

            # 🚨 CRITICAL FIX: Final aggregated decision check before any order placement logic
            if hasattr(context, 'aggregated_decision'):
                aggregated_decision = context.aggregated_decision
                if aggregated_decision and aggregated_decision.decision.value == 'WAIT':
                    logger.error(f"🚨 FINAL CHECK: Aggregated decision is WAIT - BLOCKING all order placement")
                    return True

            # 🚨 ENHANCED: Check position capacity with retry logic for favorable conditions
            if not self.has_position_capacity():
                confidence = response.get('CONFIDENCE', 50)
                logger.warning(f"No position capacity available (confidence: {confidence}%)")

                # 🚨 RETRY LOGIC: If conditions are very favorable, implement brief retry
                if confidence >= 80:
                    logger.info(f"🔄 High confidence ({confidence}%) - implementing retry logic for favorable conditions")
                    return self._retry_entry_with_favorable_conditions(response, context)
                else:
                    logger.warning("Conditions not favorable enough for retry - skipping entry")
                    return False

            # Check daily trade limits
            if not self.check_daily_limits():
                logger.warning("Daily trade limits exceeded - skipping entry")
                return False
            
            # Get trade parameters from context
            trade_params = self.build_trade_parameters(response, context)
            
            if entry_type == 'MARKET':
                return self.execute_market_entry(trade_params, context)
            else:
                return self.execute_limit_entry(trade_params, context)
            
        except Exception as e:
            logger.error(f"Error executing entry actions: {e}")
            return False

    def _retry_entry_with_favorable_conditions(self, response: Dict[str, Any], context, max_retries: int = 2) -> bool:
        """🚨 ENHANCED: Retry entry when conditions remain favorable"""
        try:
            confidence = response.get('CONFIDENCE', 50)
            action = response.get('ACTION', 'WAIT')

            logger.info(f"🔄 Implementing retry logic for high-confidence entry ({confidence}%)")

            for attempt in range(max_retries):
                # Brief delay to allow for position updates
                time.sleep(0.5)  # 500ms delay

                # Re-check position capacity
                if self.has_position_capacity():
                    logger.info(f"✅ Position capacity available on retry attempt {attempt + 1}")

                    # Re-check daily limits
                    if self.check_daily_limits():
                        # Get trade parameters and execute
                        trade_params = self.build_trade_parameters(response, context)

                        if trade_params:
                            logger.info(f"🚀 Executing entry on retry attempt {attempt + 1}")
                            return self.execute_limit_entry(trade_params, context)
                    else:
                        logger.warning(f"Daily limits exceeded on retry attempt {attempt + 1}")
                        return False
                else:
                    logger.warning(f"Still no position capacity on retry attempt {attempt + 1}")

            logger.warning(f"❌ All {max_retries} retry attempts failed - entry not executed")
            return False

        except Exception as e:
            logger.error(f"Error in retry logic: {e}")
            return False

    def track_balance_update(self, context) -> None:
        """🚨 DETAILED LOGGING: Track balance updates and calculations"""
        try:
            # Get current balance from context or main window
            current_balance = getattr(context, 'account_balance', 0.0)
            if current_balance == 0.0 and hasattr(self.main_window, 'account_balance'):
                current_balance = self.main_window.account_balance

            # Get position information for margin calculations
            positions = []
            if hasattr(self.trading_interface, 'get_open_positions'):
                try:
                    positions = self.trading_interface.get_open_positions()
                    if isinstance(positions, dict):
                        positions = list(positions.values())
                except:
                    positions = []

            # Calculate used margin
            used_margin = 0.0
            for pos in positions:
                if isinstance(pos, dict):
                    notional = pos.get('notional', 0.0)
                    leverage = pos.get('leverage', 1.0)
                    if notional > 0 and leverage > 0:
                        used_margin += notional / leverage

            available_balance = current_balance - used_margin

            # 🚨 DETAILED LOGGING: Balance and margin tracking
            logger.info(f"💰 BALANCE TRACKING:")
            logger.info(f"  📊 Total Balance: ${current_balance:.2f}")
            logger.info(f"  🔒 Used Margin: ${used_margin:.2f}")
            logger.info(f"  💵 Available Balance: ${available_balance:.2f}")
            logger.info(f"  📈 Open Positions: {len(positions)}")
            logger.info(f"  🎯 Position Capacity: {len(positions)}/{self.max_concurrent_positions}")

            # Log margin utilization percentage
            if current_balance > 0:
                margin_utilization = (used_margin / current_balance) * 100
                logger.info(f"  📊 Margin Utilization: {margin_utilization:.1f}%")

        except Exception as e:
            logger.error(f"Error tracking balance update: {e}")

    def calculate_position_size(self, symbol: str, confidence: float, context) -> float:
        """🚨 ENHANCED: Calculate appropriate position size with minimum order enforcement"""
        try:
            # 🚨 CRITICAL FIX: Get actual account balance from trading engine
            balance = 0.0

            # Try to get balance from trading interface first
            if hasattr(self.trading_interface, 'real_trading') and hasattr(self.trading_interface.real_trading, 'trading_engine'):
                try:
                    balance_data = self.trading_interface.real_trading.trading_engine.get_balance()
                    if balance_data and 'USDT' in balance_data:
                        balance = balance_data['USDT'].get('free', 0.0)
                        logger.info(f"🔍 Retrieved balance from trading engine: ${balance:.2f}")
                except Exception as e:
                    logger.warning(f"Failed to get balance from trading engine: {e}")

            # Fallback to context balance
            if balance <= 0:
                balance = getattr(context, 'account_balance', 0.0)
                if balance == 0.0 and hasattr(self.main_window, 'account_balance'):
                    balance = self.main_window.account_balance
                logger.info(f"🔍 Using context balance: ${balance:.2f}")

            # Final fallback with warning
            if balance <= 0:
                balance = 50.0  # Reduced fallback to encourage fixing the issue
                logger.error(f"⚠️ CRITICAL: No valid balance found, using minimal fallback: ${balance}")

            # Calculate base position size (conservative approach)
            # Use 1-5% of balance based on confidence
            risk_percentage = 1.0 + (confidence * 4.0)  # 1-5% based on confidence
            position_value_usd = balance * (risk_percentage / 100)

            # Get current price to calculate quantity
            current_price = getattr(context, 'current_price', 0.0)
            if current_price <= 0:
                # Fallback price for DOGE/USDT
                current_price = 0.20 if 'DOGE' in symbol else 1.0
                logger.warning(f"Using fallback price: ${current_price}")

            # Calculate quantity
            quantity = position_value_usd / current_price

            # 🚨 CRITICAL: Enforce minimum order sizes for specific symbols
            # Note: HTX contract conversion is now handled at the trading interface level
            if 'DOGE' in symbol and quantity < 100.0:
                logger.info(f"🔧 ENFORCING MINIMUM: DOGE quantity {quantity:.2f} < 100, setting to 100")
                quantity = 100.0
            elif 'BTC' in symbol and quantity < 0.001:
                logger.info(f"🔧 ENFORCING MINIMUM: BTC quantity {quantity:.6f} < 0.001, setting to 0.001")
                quantity = 0.001
            elif quantity < 1.0:
                logger.info(f"🔧 ENFORCING MINIMUM: General quantity {quantity:.6f} < 1.0, setting to 1.0")
                quantity = 1.0

            logger.info(f"💰 Position sizing: Balance=${balance:.2f}, Risk={risk_percentage:.1f}%, "
                       f"Value=${position_value_usd:.2f}, Price=${current_price:.6f}, Quantity={quantity:.6f}")

            return quantity

        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            # Fallback quantities based on symbol
            if 'DOGE' in symbol:
                return 100.0  # DOGE minimum
            elif 'BTC' in symbol:
                return 0.001  # BTC minimum
            else:
                return 10.0   # General fallback
    
    def apply_strategy_adaptations(self, response: Dict[str, Any], context) -> bool:
        """Apply strategy adaptation changes"""
        try:
            risk_adjustment = response.get('RISK_ADJUSTMENT', 1.0)
            hold_time_target = response.get('HOLD_TIME_TARGET', 8)
            entry_threshold = response.get('ENTRY_THRESHOLD', 70)
            exit_threshold = response.get('EXIT_THRESHOLD', 60)
            
            logger.info(f"Applying strategy adaptations: Risk {risk_adjustment:.1f}x, Hold time {hold_time_target}min")
            
            # Update strategy parameters in main window
            if hasattr(self.main_window, 'update_strategy_parameters'):
                self.main_window.update_strategy_parameters({
                    'risk_multiplier': risk_adjustment,
                    'target_hold_time': hold_time_target,
                    'entry_confidence_threshold': entry_threshold,
                    'exit_confidence_threshold': exit_threshold
                })
            
            return True
            
        except Exception as e:
            logger.error(f"Error applying strategy adaptations: {e}")
            return False
    
    def close_all_positions_emergency(self) -> bool:
        """Close all positions immediately using market orders"""
        try:
            if not hasattr(self.trading_interface, 'real_trading'):
                return False
            
            positions = self.trading_interface.real_trading.get_open_positions()
            
            if not positions:
                logger.info("No positions to close")
                return True
            
            logger.warning(f"EMERGENCY: Closing {len(positions)} positions")
            
            success_count = 0
            for position in positions:
                try:
                    symbol = position.get('symbol', '')
                    side = position.get('side', '')
                    size = position.get('size', 0)
                    
                    # Determine close side (opposite of position side)
                    close_side = 'sell' if side.lower() == 'long' else 'buy'
                    
                    # Execute market close order
                    result = self.trading_interface.real_trading.place_order(
                        symbol=symbol,
                        side=close_side,
                        amount=size,
                        order_type='market',
                        reduce_only=True
                    )
                    
                    if result and result.get('success'):
                        success_count += 1
                        logger.info(f"Emergency closed {symbol} {side} position")
                    else:
                        logger.error(f"Failed to close {symbol} position: {result}")
                        
                except Exception as e:
                    logger.error(f"Error closing individual position: {e}")
            
            logger.warning(f"Emergency close completed: {success_count}/{len(positions)} positions closed")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error in emergency close all: {e}")
            return False
    
    def close_losing_positions(self) -> bool:
        """Close only losing positions"""
        try:
            if not hasattr(self.trading_interface, 'real_trading'):
                return False
            
            positions = self.trading_interface.real_trading.get_open_positions()
            losing_positions = [pos for pos in positions if pos.get('unrealized_pnl', 0) < 0]
            
            if not losing_positions:
                logger.info("No losing positions to close")
                return True
            
            logger.warning(f"Closing {len(losing_positions)} losing positions")
            
            success_count = 0
            for position in losing_positions:
                if self.close_single_position(position):
                    success_count += 1
            
            logger.info(f"Closed {success_count}/{len(losing_positions)} losing positions")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error closing losing positions: {e}")
            return False
    
    def close_position_full(self, context) -> bool:
        """Close a position completely"""
        try:
            # Get the most critical position from context
            if not context.open_positions:
                return True
            
            position = context.open_positions[0]  # Assume first position is target
            return self.close_single_position(position)
            
        except Exception as e:
            logger.error(f"Error closing position full: {e}")
            return False
    
    def close_position_partial(self, context, close_percentage: float) -> bool:
        """Close a percentage of a position"""
        try:
            if not context.open_positions:
                return True
            
            position = context.open_positions[0]
            original_size = position.get('size', 0)
            close_size = original_size * (close_percentage / 100)
            
            if close_size <= 0:
                return True
            
            symbol = position.get('symbol', '')
            side = position.get('side', '')
            close_side = 'sell' if side.lower() == 'long' else 'buy'
            
            logger.info(f"Partial close: {close_percentage}% of {symbol} position ({close_size:.4f})")
            
            result = self.trading_interface.real_trading.place_order(
                symbol=symbol,
                side=close_side,
                amount=close_size,
                order_type='market',
                reduce_only=True
            )
            
            if result and result.get('success'):
                logger.info(f"Partial close successful: {close_percentage}% of {symbol}")
                return True
            else:
                logger.error(f"Partial close failed: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Error in partial close: {e}")
            return False
    
    def close_single_position(self, position: Dict[str, Any]) -> bool:
        """Close a single position"""
        try:
            symbol = position.get('symbol', '')
            side = position.get('side', '')
            size = position.get('size', 0)
            
            close_side = 'sell' if side.lower() == 'long' else 'buy'
            
            # CRITICAL: Use LIMIT orders only - get current price for close order
            current_price = self.trading_interface.real_trading.get_current_price(symbol)
            if not current_price:
                logger.error(f"Could not get current price for {symbol} - cannot close position")
                return False

            # Calculate aggressive LIMIT price for quick execution
            # For closing positions, we want fast execution so use aggressive pricing
            if close_side == 'sell':
                # Selling: price slightly below market for quick execution
                limit_price = current_price * 0.999  # 0.1% below market
            else:
                # Buying: price slightly above market for quick execution
                limit_price = current_price * 1.001  # 0.1% above market

            result = self.trading_interface.real_trading.place_order(
                symbol=symbol,
                side=close_side,
                amount=size,
                order_type='limit',  # CRITICAL: LIMIT orders only
                price=limit_price,
                reduce_only=True
            )
            
            if result and result.get('success'):
                logger.info(f"Closed {symbol} {side} position")
                return True
            else:
                logger.error(f"Failed to close {symbol} position: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Error closing single position: {e}")
            return False
    
    def has_position_capacity(self) -> bool:
        """🚨 CRITICAL FIX: Check if we have capacity for new positions"""
        try:
            # 🚨 FIX #1: Enhanced position checking with multiple fallbacks
            positions = []
            position_count = 0

            # Method 1: Direct get_open_positions() method
            if hasattr(self.trading_interface, 'get_open_positions'):
                try:
                    positions = self.trading_interface.get_open_positions()
                    if isinstance(positions, dict):
                        position_count = len(positions)
                    elif isinstance(positions, list):
                        position_count = len(positions)
                    else:
                        position_count = 0

                    # 🚨 DETAILED LOGGING: Track position capacity calculations
                    logger.info(f"✅ Position capacity check (direct): {position_count} open positions, max: {self.max_concurrent_positions}")
                    logger.info(f"📊 Position details: {list(positions.keys()) if isinstance(positions, dict) else 'N/A'}")
                    logger.info(f"🎯 Capacity available: {position_count < self.max_concurrent_positions}")

                    return position_count < self.max_concurrent_positions
                except Exception as e:
                    logger.warning(f"Direct position check failed: {e}")

            # Method 2: Through real_trading attribute (legacy)
            if hasattr(self.trading_interface, 'real_trading') and self.trading_interface.real_trading:
                try:
                    positions = self.trading_interface.real_trading.get_open_positions()
                    if isinstance(positions, dict):
                        position_count = len(positions)
                    elif isinstance(positions, list):
                        position_count = len(positions)
                    else:
                        position_count = 0

                    logger.info(f"✅ Position capacity check (legacy): {position_count} open positions, max: {self.max_concurrent_positions}")
                    return position_count < self.max_concurrent_positions
                except Exception as e:
                    logger.warning(f"Legacy position check failed: {e}")

            # Method 3: Check through main window positions table
            if hasattr(self.trading_interface, 'positions_table'):
                try:
                    position_count = self.trading_interface.positions_table.rowCount()
                    logger.info(f"✅ Position capacity check (GUI table): {position_count} open positions, max: {self.max_concurrent_positions}")
                    return position_count < self.max_concurrent_positions
                except Exception as e:
                    logger.warning(f"GUI table position check failed: {e}")

            # Method 4: Check through account state
            if hasattr(self.trading_interface, 'get_account_state'):
                try:
                    account_state = self.trading_interface.get_account_state()
                    if account_state and 'open_positions' in account_state:
                        position_count = len(account_state['open_positions'])
                        logger.info(f"✅ Position capacity check (account state): {position_count} open positions, max: {self.max_concurrent_positions}")
                        return position_count < self.max_concurrent_positions
                except Exception as e:
                    logger.warning(f"Account state position check failed: {e}")

            # 🚨 CRITICAL FIX: Default to allowing trades if we can't check positions
            # This prevents the system from being stuck in "no capacity" mode
            logger.warning(f"⚠️ Cannot reliably check open positions - ALLOWING trade (assuming capacity available)")
            logger.warning(f"⚠️ This is a fallback to prevent trading system lockup")
            return True

        except Exception as e:
            logger.error(f"❌ Error checking position capacity: {e}")
            # 🚨 CRITICAL: Allow trading if check fails to prevent system lockup
            logger.warning(f"⚠️ Position capacity check failed - ALLOWING trade to prevent lockup")
            return True
    
    def check_daily_limits(self) -> bool:
        """Check if daily trading limits are exceeded"""
        # Reset daily counters if new day
        current_time = time.time()
        if current_time - self.last_trade_time > 86400:  # 24 hours
            self.daily_trades = 0
            self.daily_loss = 0.0
        
        # Check limits
        if self.daily_trades >= self.max_daily_trades:
            return False
        
        if self.daily_loss >= self.max_daily_loss:
            return False
        
        return True
    
    def build_trade_parameters(self, response: Dict[str, Any], context) -> Dict[str, Any]:
        """🚨 ENHANCED: Build trade parameters with proper position sizing and minimum order adjustment"""
        try:
            # Get basic parameters
            symbol = context.symbol
            confidence = response.get('CONFIDENCE', 50) / 100.0  # Convert to 0-1 scale
            entry_type = response.get('ENTRY_TYPE', 'LIMIT')

            # Determine trade direction from aggregated decision
            side = 'buy'  # Default
            if hasattr(context, 'aggregated_decision') and context.aggregated_decision:
                decision = context.aggregated_decision.decision.value
                if decision == 'SHORT':
                    side = 'sell'
                elif decision == 'LONG':
                    side = 'buy'

            # Calculate position size using available systems
            quantity = self.calculate_position_size(symbol, confidence, context)

            # 🚨 AUTO-ADJUST: Ensure minimum order size requirements
            if hasattr(self.trading_interface, 'validate_and_adjust_minimum_order_size'):
                valid, adjusted_quantity = self.trading_interface.validate_and_adjust_minimum_order_size(symbol, quantity)
                if adjusted_quantity != quantity:
                    logger.info(f"🔧 AUTO-ADJUSTED: Position size from {quantity:.8f} to {adjusted_quantity:.8f} for {symbol}")
                    quantity = adjusted_quantity

            return {
                'symbol': symbol,
                'side': side,
                'quantity': quantity,
                'entry_type': entry_type,
                'confidence': confidence * 100  # Convert back to percentage
            }

        except Exception as e:
            logger.error(f"Error building trade parameters: {e}")
            # Fallback with minimum order size for DOGE/USDT
            fallback_quantity = 100.0 if 'DOGE' in context.symbol else 10.0
            return {
                'symbol': context.symbol,
                'side': 'buy',
                'quantity': fallback_quantity,
                'entry_type': response.get('ENTRY_TYPE', 'LIMIT'),
                'confidence': response.get('CONFIDENCE', 50)
            }
    
    def execute_market_entry(self, trade_params: Dict[str, Any], context) -> bool:
        """🚀 ENHANCED: Execute intelligent limit order instead of market order"""
        try:
            # 🚀 CRITICAL: Replace market orders with intelligent limit orders
            logger.warning("⚠️ Market order requested - converting to intelligent limit order for better execution")

            # Use intelligent limit order system instead
            return self.execute_intelligent_limit_entry(trade_params, context, aggressive=True)

        except Exception as e:
            logger.error(f"Error in market entry conversion: {e}")
            return False

    def execute_limit_entry(self, trade_params: Dict[str, Any], context) -> bool:
        """🚀 ENHANCED: Execute intelligent limit order with professional scalping logic"""
        try:
            return self.execute_intelligent_limit_entry(trade_params, context, aggressive=False)
        except Exception as e:
            logger.error(f"Error in limit entry: {e}")
            return False

    def execute_intelligent_limit_entry(self, trade_params: Dict[str, Any], context, aggressive: bool = False) -> bool:
        """🚀 NEW: Execute intelligent limit order using professional scalping system"""
        try:
            # 🚨 CRITICAL FIX: Check aggregated vote decision before placing any orders
            if hasattr(context, 'aggregated_decision'):
                aggregated_decision = context.aggregated_decision
                if aggregated_decision and aggregated_decision.decision.value == 'WAIT':
                    logger.info(f"🛑 Aggregated decision is WAIT ({aggregated_decision.confidence:.1f}%) - skipping intelligent limit order")
                    logger.info(f"🛑 Reasoning: {aggregated_decision.reasoning}")
                    return True  # Don't place order, but don't fail

            symbol = trade_params.get('symbol', '')
            side = trade_params.get('side', 'buy')
            quantity = trade_params.get('quantity', 0)
            confidence = trade_params.get('confidence', 85.0)

            if not symbol or quantity <= 0:
                logger.error("Invalid trade parameters for intelligent limit order")
                return False

            # 🚨 HTX CONTRACT FIX: Apply contract conversion for intelligent limit orders
            if 'DOGE' in symbol and hasattr(self.trading_interface, 'real_trading'):
                if hasattr(self.trading_interface.real_trading, 'trading_engine'):
                    if hasattr(self.trading_interface.real_trading.trading_engine, 'exchange_name'):
                        if self.trading_interface.real_trading.trading_engine.exchange_name == "htx":
                            original_quantity = quantity
                            quantity = max(1.0, quantity / 100.0)  # Convert to contracts
                            logger.info(f"🔧 HTX INTELLIGENT ORDER CONVERSION: {original_quantity:.8f} DOGE → {quantity:.8f} contracts")
                            print(f"🔧 HTX INTELLIGENT ORDER CONVERSION: {original_quantity:.8f} DOGE → {quantity:.8f} contracts")

            # 🚀 ENHANCED: Use intelligent limit order manager if available
            if self.limit_order_manager:
                logger.info(f"🎯 Placing intelligent limit order: {side.upper()} {quantity} {symbol} (confidence: {confidence:.1f}%)")

                # Cancel any existing orders for this symbol first (clean slate)
                if self.scalping_config['auto_cancel_stale_orders']:
                    cancelled_count = self.limit_order_manager.cancel_all_orders(symbol, "new_order_cleanup")
                    if cancelled_count > 0:
                        logger.info(f"🧹 Cancelled {cancelled_count} stale orders for {symbol}")

                # Adjust confidence for aggressive mode
                if aggressive:
                    confidence = min(95.0, confidence + 10.0)  # Boost confidence for aggressive fills
                    logger.info(f"🚀 Aggressive mode: boosted confidence to {confidence:.1f}%")

                # Place intelligent limit order
                smart_order = self.limit_order_manager.place_smart_limit_order(
                    symbol=symbol,
                    side=side,
                    amount=quantity,
                    confidence=confidence,
                    timeout_seconds=self.scalping_config['order_timeout_seconds']
                )

                if smart_order:
                    # Update execution tracking
                    self.daily_trades += 1
                    self.last_trade_time = time.time()

                    # Log successful order placement
                    logger.info(f"✅ Intelligent limit order placed: {smart_order.id}")
                    logger.info(f"   📊 {side.upper()} {quantity} {symbol} @ ${smart_order.price:.6f}")
                    logger.info(f"   ⏰ Timeout: {smart_order.timeout_seconds}s | Confidence: {confidence:.1f}%")

                    # Add to execution log
                    self.execution_log.append({
                        'timestamp': time.time(),
                        'action': 'intelligent_limit_entry',
                        'symbol': symbol,
                        'side': side,
                        'quantity': quantity,
                        'price': smart_order.price,
                        'order_id': smart_order.id,
                        'confidence': confidence,
                        'aggressive': aggressive
                    })

                    return True
                else:
                    logger.error(f"❌ Failed to place intelligent limit order for {symbol}")
                    return False

            else:
                # 🚀 FALLBACK: Use traditional limit order if intelligent system not available
                logger.warning("⚠️ Intelligent limit order manager not available - using fallback")
                return self.execute_fallback_limit_order(trade_params, context)

        except Exception as e:
            logger.error(f"Error in intelligent limit entry: {e}")
            return False

    def execute_fallback_limit_order(self, trade_params: Dict[str, Any], context) -> bool:
        """Fallback limit order execution when intelligent system is not available"""
        try:
            symbol = trade_params.get('symbol', '')
            side = trade_params.get('side', 'buy')
            quantity = trade_params.get('quantity', 0)

            # Validate inputs
            if not symbol or quantity <= 0:
                logger.error("Invalid trade parameters for fallback limit order")
                return False

            # HTX Contract Conversion
            if hasattr(self.trading_interface, 'real_trading'):
                quantity = self.trading_interface.real_trading.convert_to_htx_contract_format(symbol, quantity)
                logger.info(f"Converted quantity to HTX contract format: {quantity}")

            # Place fallback limit order
            if side == 'buy':
                result = self.trading_interface.real_trading.place_limit_long(symbol, quantity, leverage=20)
            else:
                result = self.trading_interface.real_trading.place_limit_short(symbol, quantity, leverage=20)

            if result:
                logger.info(f"Fallback limit order placed successfully: {result}")
                return True
            else:
                logger.error("Fallback limit order failed")
                return False

        except Exception as e:
            logger.error(f"Error in fallback limit order: {e}")
            return False

    def create_hedge_positions(self, context) -> bool:
        """Create hedge positions to reduce risk"""
        try:
            logger.info("Creating hedge positions - not implemented yet")
            return True
        except Exception as e:
            logger.error(f"Error creating hedge positions: {e}")
            return False

    def reduce_position_sizes(self) -> bool:
        """Reduce all position sizes by 50%"""
        try:
            if not hasattr(self.trading_interface, 'real_trading'):
                return False

            positions = self.trading_interface.real_trading.get_open_positions()

            if not positions:
                logger.info("No positions to reduce")
                return True

            logger.warning(f"Reducing {len(positions)} position sizes by 50%")

            success_count = 0
            for position in positions:
                try:
                    symbol = position.get('symbol', '')
                    side = position.get('side', '')
                    current_size = position.get('size', 0)
                    reduce_size = current_size * 0.5  # Reduce by 50%

                    if reduce_size <= 0:
                        continue

                    # Determine close side (opposite of position side)
                    close_side = 'sell' if side.lower() == 'long' else 'buy'

                    # Execute partial close order
                    result = self.trading_interface.real_trading.place_order(
                        symbol=symbol,
                        side=close_side,
                        amount=reduce_size,
                        order_type='market',
                        reduce_only=True
                    )

                    if result and result.get('success'):
                        success_count += 1
                        logger.info(f"Reduced {symbol} {side} position by 50%")
                    else:
                        logger.error(f"Failed to reduce {symbol} position: {result}")

                except Exception as e:
                    logger.error(f"Error reducing individual position: {e}")

            logger.info(f"Position reduction completed: {success_count}/{len(positions)} positions reduced")
            return success_count > 0

        except Exception as e:
            logger.error(f"Error reducing position sizes: {e}")
            return False

    def update_position_levels(self, response: Dict[str, Any], context) -> bool:
        """Update stop loss and take profit levels"""
        try:
            stop_loss = response.get('STOP_LOSS', 0)
            take_profit = response.get('TAKE_PROFIT', 0)

            logger.info(f"Updating position levels - Stop: {stop_loss}, TP: {take_profit}")
            # Implementation would update stop loss and take profit orders
            return True
        except Exception as e:
            logger.error(f"Error updating position levels: {e}")
            return False
    
    def set_trailing_stop(self, response: Dict[str, Any], context) -> bool:
        """Set trailing stop for position"""
        try:
            new_stop = response.get('NEW_STOP', 0)
            logger.info(f"Setting trailing stop at {new_stop}")
            # Implementation would set trailing stop order
            return True
        except Exception as e:
            logger.error(f"Error setting trailing stop: {e}")
            return False

    # 🚀 NEW: Intelligent limit order management methods

    def cancel_all_pending_orders(self, symbol: str = None, reason: str = "manual") -> int:
        """Cancel all pending limit orders"""
        try:
            if self.limit_order_manager:
                cancelled_count = self.limit_order_manager.cancel_all_orders(symbol, reason)
                logger.info(f"✅ Cancelled {cancelled_count} pending orders (reason: {reason})")
                return cancelled_count
            else:
                logger.warning("⚠️ Limit order manager not available for cancellation")
                return 0
        except Exception as e:
            logger.error(f"Error cancelling pending orders: {e}")
            return 0

    def get_active_orders_status(self, symbol: str = None) -> Dict[str, Any]:
        """Get status of active limit orders"""
        try:
            if self.limit_order_manager:
                active_orders = self.limit_order_manager.get_active_orders(symbol)
                stats = self.limit_order_manager.get_order_stats()

                return {
                    'active_orders_count': len(active_orders),
                    'active_orders': [
                        {
                            'id': order.id,
                            'symbol': order.symbol,
                            'side': order.side,
                            'amount': order.amount,
                            'price': order.price,
                            'status': order.status.value,
                            'filled_amount': order.filled_amount,
                            'remaining_amount': order.remaining_amount,
                            'age_seconds': (datetime.now() - order.created_at).total_seconds(),
                            'replacement_count': order.replacement_count
                        } for order in active_orders
                    ],
                    'stats': stats
                }
            else:
                return {'active_orders_count': 0, 'active_orders': [], 'stats': {}}
        except Exception as e:
            logger.error(f"Error getting active orders status: {e}")
            return {'active_orders_count': 0, 'active_orders': [], 'stats': {}}

    def emergency_cancel_all_orders(self, reason: str = "emergency") -> bool:
        """Emergency cancellation of all limit orders"""
        try:
            if self.limit_order_manager:
                cancelled_count = self.limit_order_manager.emergency_cancel_all(reason)
                logger.warning(f"🚨 EMERGENCY: Cancelled {cancelled_count} orders - {reason}")
                return cancelled_count > 0
            else:
                logger.warning("⚠️ Limit order manager not available for emergency cancellation")
                return False
        except Exception as e:
            logger.error(f"Error in emergency order cancellation: {e}")
            return False

    def get_order_book_analysis(self, symbol: str) -> Dict[str, Any]:
        """Get order book analysis for trading decisions"""
        try:
            if self.limit_order_manager:
                return self.limit_order_manager.get_order_book_analysis(symbol)
            else:
                logger.warning("⚠️ Limit order manager not available for order book analysis")
                return {}
        except Exception as e:
            logger.error(f"Error getting order book analysis: {e}")
            return {}

    def is_market_suitable_for_scalping(self, symbol: str) -> bool:
        """Check if market conditions are suitable for scalping with limit orders"""
        try:
            analysis = self.get_order_book_analysis(symbol)

            if not analysis:
                return False

            # Check spread conditions
            spread_ok = analysis.get('spread_pct', 100) <= self.scalping_config['max_spread_pct']

            # Check liquidity
            liquidity_ok = analysis.get('liquidity_sufficient', False)

            # Check if market is tradeable
            tradeable = analysis.get('is_tradeable', False)

            suitable = spread_ok and liquidity_ok and tradeable

            if not suitable:
                logger.warning(f"⚠️ Market not suitable for scalping: spread={analysis.get('spread_pct', 0):.3f}%, liquidity={liquidity_ok}")

            return suitable

        except Exception as e:
            logger.error(f"Error checking market suitability: {e}")
            return False

    def get_execution_summary(self) -> Dict[str, Any]:
        """Get summary of execution performance"""
        try:
            # Get limit order manager stats
            order_stats = {}
            if self.limit_order_manager:
                order_stats = self.limit_order_manager.get_order_stats()

            # Combine with execution log stats
            recent_executions = [log for log in self.execution_log if time.time() - log['timestamp'] < 3600]  # Last hour

            return {
                'daily_trades': self.daily_trades,
                'daily_loss': self.daily_loss,
                'recent_executions_count': len(recent_executions),
                'limit_order_stats': order_stats,
                'scalping_config': self.scalping_config,
                'last_trade_time': self.last_trade_time,
                'execution_log_size': len(self.execution_log)
            }

        except Exception as e:
            logger.error(f"Error getting execution summary: {e}")
            return {}
